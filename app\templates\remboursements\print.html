{% extends "print_layout.html" %}

{% block content %}
<div class="info-section">
    <h2>Informations Générales</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Entreprise:</span>
                <span>{{ remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Organisme:</span>
                <span>{{ remboursement.organisme.raison_sociale if remboursement.organisme else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Formateur:</span>
                <span>{{ remboursement.formateur.nom_prenom if remboursement.formateur else 'N/A' }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Thème:</span>
                <span>{{ remboursement.theme }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Date:</span>
                <span>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Numéro de Contrat:</span>
                <span>{{ remboursement.num_contrat }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Financières</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Montant:</span>
                <span>{{ remboursement.montant }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Mode de Règlement:</span>
                <span>{{ remboursement.mode_de_reglement }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Numéro de Chèque/Virement:</span>
                <span>{{ remboursement.num_cheque_virement }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Date de Règlement:</span>
                <span>{{ remboursement.date_reglement.strftime('%d/%m/%Y') if remboursement.date_reglement else 'N/A' }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Documents</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Contrat:</span>
                <span>{{ 'Oui' if remboursement.contrat else 'Non' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Facture:</span>
                <span>{{ 'Oui' if remboursement.facture else 'Non' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Attestation:</span>
                <span>{{ 'Oui' if remboursement.attestation else 'Non' }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Feuille de Présence:</span>
                <span>{{ 'Oui' if remboursement.feuille_presence else 'Non' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Formulaire GIAC:</span>
                <span>{{ 'Oui' if remboursement.formulaire_giac else 'Non' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Rapport:</span>
                <span>{{ 'Oui' if remboursement.rapport else 'Non' }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Complémentaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Date de Création:</span>
                <span>{{ remboursement.date_creation.strftime('%d/%m/%Y') if remboursement.date_creation else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Statut:</span>
                <span>{{ remboursement.statut }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Créé par:</span>
                <span>{{ remboursement.user.nom_complet if remboursement.user else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Notes:</span>
                <span>{{ remboursement.notes }}</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}
