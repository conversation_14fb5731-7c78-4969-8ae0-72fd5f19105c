{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Restaurer la Base de Données
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>ATTENTION - Action Irréversible</h5>
                        <p class="mb-0">
                            La restauration remplacera <strong>complètement</strong> la base de données actuelle par celle du fichier de sauvegarde. 
                            <strong>Toutes les données actuelles seront perdues définitivement.</strong>
                        </p>
                    </div>
                    
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-4">
                            {{ form.fichier_sauvegarde.label(class="form-label") }}
                            {{ form.fichier_sauvegarde(class="form-control") }}
                            {% if form.fichier_sauvegarde.errors %}
                                <div class="text-danger">
                                    {% for error in form.fichier_sauvegarde.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Sélectionnez un fichier de sauvegarde (.db, .sql, .sqlite, .backup)
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.confirmer(class="form-check-input") }}
                                {{ form.confirmer.label(class="form-check-label text-danger") }}
                            </div>
                            {% if form.confirmer.errors %}
                                <div class="text-danger">
                                    {% for error in form.confirmer.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('sauvegardes') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Retour
                            </a>
                            {{ form.submit(class="btn btn-danger", onclick="return confirmerRestauration()") }}
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Étapes de restauration -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list-ol me-2"></i>
                        Processus de Restauration
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <ol class="list-group list-group-numbered">
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Sauvegarde de sécurité</div>
                                        Une copie de la base actuelle sera créée automatiquement
                                    </div>
                                    <span class="badge bg-info rounded-pill">Auto</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Vérification du fichier</div>
                                        Le fichier de sauvegarde sera vérifié et validé
                                    </div>
                                    <span class="badge bg-warning rounded-pill">Validation</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Restauration</div>
                                        Remplacement de la base de données actuelle
                                    </div>
                                    <span class="badge bg-danger rounded-pill">Critique</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">Redémarrage</div>
                                        Reconnexion nécessaire après la restauration
                                    </div>
                                    <span class="badge bg-success rounded-pill">Final</span>
                                </li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Conseils de sécurité -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Conseils de Sécurité
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-check-circle me-1 text-success"></i> Avant la Restauration</h6>
                            <ul class="text-muted small">
                                <li>Créez une sauvegarde manuelle de la base actuelle</li>
                                <li>Vérifiez que le fichier de sauvegarde est intact</li>
                                <li>Informez les autres utilisateurs de la maintenance</li>
                                <li>Testez la restauration sur un environnement de test si possible</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-exclamation-circle me-1 text-warning"></i> Après la Restauration</h6>
                            <ul class="text-muted small">
                                <li>Vérifiez l'intégrité des données restaurées</li>
                                <li>Testez les fonctionnalités principales</li>
                                <li>Informez les utilisateurs que le système est opérationnel</li>
                                <li>Documentez la restauration dans le journal</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerRestauration() {
    return confirm('DERNIÈRE CONFIRMATION : Êtes-vous absolument certain de vouloir restaurer la base de données ? Cette action supprimera définitivement toutes les données actuelles.');
}
</script>
{% endblock %}
