from flask import Flask
from config import Config
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
import os
import sys

# Fonction pour obtenir le chemin de l'application, fonctionne aussi avec PyInstaller
def resource_path(relative_path):
    """ Obtenir le chemin absolu des ressources, fonctionne pour dev et pour PyInstaller """
    try:
        # PyInstaller crée un dossier temporaire et stocke le chemin dans _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

# تهيئة الإضافات
db = SQLAlchemy()
login_manager = LoginManager()
login_manager.login_view = 'login'
login_manager.login_message = "Veuillez vous connecter pour accéder à cette page."
login_manager.login_message_category = "info"

def create_app(config_class=Config):
    # Déterminer les chemins des dossiers statiques et templates
    static_folder = resource_path('app/static')
    template_folder = resource_path('app/templates')

    app = Flask(__name__, static_folder=static_folder, template_folder=template_folder)
    app.config.from_object(config_class)

    # تهيئة الإضافات مع التطبيق
    db.init_app(app)
    login_manager.init_app(app)

    # تسجيل المسارات
    with app.app_context():
        from app.routes import (
            index, login, logout, dashboard,
            formations, new_formation, edit_formation, delete_formation,
            dossiers_techniques, new_dossier_technique, edit_dossier_technique, delete_dossier_technique,
            remboursements, new_remboursement, edit_remboursement, delete_remboursement,
            organismes, new_organisme, edit_organisme, delete_organisme,
            formateurs, new_formateur, edit_formateur, delete_formateur,
            users, new_user, edit_user, delete_user,
            domaines_themes, new_domaine, edit_domaine, delete_domaine,
            new_theme, edit_theme, delete_theme,
            agenda_formateurs, new_agenda, edit_agenda, delete_agenda,
            generate_report,
            fiches_inscription, new_fiche_inscription, edit_fiche_inscription, delete_fiche_inscription,
            eligibilite_ofppt,
            search_fiches, search_dossiers, search_remboursements, search_organismes, search_formateurs, search_domaines_themes, search_users,
            print_fiche_inscription, print_dossier_technique, print_remboursement, print_organisme, print_formateur, print_agenda,
            sauvegardes, nouvelle_sauvegarde, restaurer_sauvegarde, supprimer_sauvegarde, telecharger_sauvegarde,
            journal_activite, exporter_journal, vider_journal, config_sauvegarde_auto,
            company_info, edit_company_info, print_example
        )

        # Routes principales
        app.add_url_rule('/', 'index', index)
        app.add_url_rule('/index', 'index', index)
        app.add_url_rule('/login', 'login', login, methods=['GET', 'POST'])
        app.add_url_rule('/logout', 'logout', logout)
        app.add_url_rule('/dashboard', 'dashboard', dashboard)

        # Routes pour les formations
        app.add_url_rule('/formations', 'formations', formations)
        app.add_url_rule('/formations/new', 'new_formation', new_formation, methods=['GET', 'POST'])
        app.add_url_rule('/formations/edit/<int:id>', 'edit_formation', edit_formation, methods=['GET', 'POST'])
        app.add_url_rule('/formations/delete/<int:id>', 'delete_formation', delete_formation)

        # Routes pour les dossiers techniques
        app.add_url_rule('/dossiers-techniques', 'dossiers_techniques', dossiers_techniques)
        app.add_url_rule('/dossiers-techniques/new', 'new_dossier_technique', new_dossier_technique, methods=['GET', 'POST'])
        app.add_url_rule('/dossiers-techniques/edit/<int:id>', 'edit_dossier_technique', edit_dossier_technique, methods=['GET', 'POST'])
        app.add_url_rule('/dossiers-techniques/delete/<int:id>', 'delete_dossier_technique', delete_dossier_technique)

        # Routes pour les remboursements
        app.add_url_rule('/remboursements', 'remboursements', remboursements)
        app.add_url_rule('/remboursements/new', 'new_remboursement', new_remboursement, methods=['GET', 'POST'])
        app.add_url_rule('/remboursements/edit/<int:id>', 'edit_remboursement', edit_remboursement, methods=['GET', 'POST'])
        app.add_url_rule('/remboursements/delete/<int:id>', 'delete_remboursement', delete_remboursement)

        # Routes pour les organismes
        app.add_url_rule('/organismes', 'organismes', organismes)
        app.add_url_rule('/organismes/new', 'new_organisme', new_organisme, methods=['GET', 'POST'])
        app.add_url_rule('/organismes/edit/<int:id>', 'edit_organisme', edit_organisme, methods=['GET', 'POST'])
        app.add_url_rule('/organismes/delete/<int:id>', 'delete_organisme', delete_organisme)

        # Routes pour les formateurs
        app.add_url_rule('/formateurs', 'formateurs', formateurs)
        app.add_url_rule('/formateurs/new', 'new_formateur', new_formateur, methods=['GET', 'POST'])
        app.add_url_rule('/formateurs/edit/<int:id>', 'edit_formateur', edit_formateur, methods=['GET', 'POST'])
        app.add_url_rule('/formateurs/delete/<int:id>', 'delete_formateur', delete_formateur)

        # Routes pour les utilisateurs
        app.add_url_rule('/users', 'users', users)
        app.add_url_rule('/users/new', 'new_user', new_user, methods=['GET', 'POST'])
        app.add_url_rule('/users/edit/<int:id>', 'edit_user', edit_user, methods=['GET', 'POST'])
        app.add_url_rule('/users/delete/<int:id>', 'delete_user', delete_user)

        # Routes pour les domaines et thèmes
        app.add_url_rule('/domaines-themes', 'domaines_themes', domaines_themes)
        app.add_url_rule('/domaines/new', 'new_domaine', new_domaine, methods=['GET', 'POST'])
        app.add_url_rule('/domaines/edit/<int:id>', 'edit_domaine', edit_domaine, methods=['GET', 'POST'])
        app.add_url_rule('/domaines/delete/<int:id>', 'delete_domaine', delete_domaine)

        app.add_url_rule('/themes/new', 'new_theme', new_theme, methods=['GET', 'POST'])
        app.add_url_rule('/themes/edit/<int:id>', 'edit_theme', edit_theme, methods=['GET', 'POST'])
        app.add_url_rule('/themes/delete/<int:id>', 'delete_theme', delete_theme)

        # Routes pour l'agenda des formateurs
        app.add_url_rule('/agenda', 'agenda_formateurs', agenda_formateurs)
        app.add_url_rule('/agenda/new', 'new_agenda', new_agenda, methods=['GET', 'POST'])
        app.add_url_rule('/agenda/edit/<int:id>', 'edit_agenda', edit_agenda, methods=['GET', 'POST'])
        app.add_url_rule('/agenda/delete/<int:id>', 'delete_agenda', delete_agenda)

        # Route pour générer des rapports
        app.add_url_rule('/reports/<string:type>', 'generate_report', generate_report)

        # Routes pour les fiches d'inscription
        app.add_url_rule('/fiches-inscription', 'fiches_inscription', fiches_inscription)
        app.add_url_rule('/fiches-inscription/new', 'new_fiche_inscription', new_fiche_inscription, methods=['GET', 'POST'])
        app.add_url_rule('/fiches-inscription/edit/<int:id>', 'edit_fiche_inscription', edit_fiche_inscription, methods=['GET', 'POST'])
        app.add_url_rule('/fiches-inscription/delete/<int:id>', 'delete_fiche_inscription', delete_fiche_inscription)

        # Route pour l'éligibilité OFPPT
        app.add_url_rule('/eligibilite-ofppt', 'eligibilite_ofppt', eligibilite_ofppt)

        # Routes pour la recherche
        app.add_url_rule('/fiches-inscription/search', 'search_fiches', search_fiches)
        app.add_url_rule('/dossiers-techniques/search', 'search_dossiers', search_dossiers)
        app.add_url_rule('/remboursements/search', 'search_remboursements', search_remboursements)
        app.add_url_rule('/organismes/search', 'search_organismes', search_organismes)
        app.add_url_rule('/formateurs/search', 'search_formateurs', search_formateurs)
        app.add_url_rule('/domaines-themes/search', 'search_domaines_themes', search_domaines_themes)
        app.add_url_rule('/users/search', 'search_users', search_users)

        # Routes pour l'impression
        app.add_url_rule('/fiches-inscription/print/<int:id>', 'print_fiche_inscription', print_fiche_inscription)
        app.add_url_rule('/dossiers-techniques/print/<int:id>', 'print_dossier_technique', print_dossier_technique)
        app.add_url_rule('/remboursements/print/<int:id>', 'print_remboursement', print_remboursement)
        app.add_url_rule('/organismes/print/<int:id>', 'print_organisme', print_organisme)
        app.add_url_rule('/formateurs/print/<int:id>', 'print_formateur', print_formateur)
        app.add_url_rule('/agenda/print/<int:id>', 'print_agenda', print_agenda)

        # Routes pour la gestion des sauvegardes
        app.add_url_rule('/sauvegardes', 'sauvegardes', sauvegardes)
        app.add_url_rule('/sauvegardes/nouvelle', 'nouvelle_sauvegarde', nouvelle_sauvegarde, methods=['GET', 'POST'])
        app.add_url_rule('/sauvegardes/restaurer', 'restaurer_sauvegarde', restaurer_sauvegarde, methods=['GET', 'POST'])
        app.add_url_rule('/sauvegardes/supprimer/<int:id>', 'supprimer_sauvegarde', supprimer_sauvegarde)
        app.add_url_rule('/sauvegardes/telecharger/<int:id>', 'telecharger_sauvegarde', telecharger_sauvegarde)
        app.add_url_rule('/sauvegardes/config-auto', 'config_sauvegarde_auto', config_sauvegarde_auto, methods=['GET', 'POST'])

        # Routes pour le journal d'activité
        app.add_url_rule('/journal-activite', 'journal_activite', journal_activite, methods=['GET', 'POST'])
        app.add_url_rule('/journal-activite/exporter', 'exporter_journal', exporter_journal)
        app.add_url_rule('/journal-activite/vider', 'vider_journal', vider_journal)

        # Routes pour les informations de l'entreprise
        app.add_url_rule('/company-info', 'company_info', company_info)
        app.add_url_rule('/company-info/edit', 'edit_company_info', edit_company_info, methods=['GET', 'POST'])
        app.add_url_rule('/print-example', 'print_example', print_example)

        # Context processors pour les templates (désactivé temporairement)
        try:
            from app.context_processors import inject_company_info, inject_print_helpers
            app.context_processor(inject_company_info)
            app.context_processor(inject_print_helpers)
            print("✅ Context processors chargés")
        except Exception as e:
            print(f"⚠️ Erreur context processors: {e}")

        # Démarrer le planificateur de sauvegardes automatiques (désactivé temporairement)
        try:
            # from app.utils import demarrer_planificateur_sauvegardes
            # demarrer_planificateur_sauvegardes()
            print("🕐 Planificateur de sauvegardes automatiques (désactivé)")
        except Exception as e:
            print(f"⚠️ Erreur lors du démarrage du planificateur: {e}")

    return app
