{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-sitemap"></i> Domaines et Thèmes</h1>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-group" role="group">
                <a href="{{ url_for('new_domaine') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Ajouter un Domaine
                </a>
                <a href="{{ url_for('new_theme') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> Ajouter un Thème
                </a>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_domaines_themes') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un domaine ou un thème..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Liste des Domaines</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 15%;">ID</th>
                                    <th style="width: 55%;">Nom</th>
                                    <th style="width: 30%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for domaine in domaines %}
                                <tr class="align-middle">
                                    <td><strong>{{ domaine.id }}</strong></td>
                                    <td>
                                        <i class="fas fa-folder text-primary me-2"></i>
                                        <strong>{{ domaine.nom }}</strong>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_domaine', id=domaine.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDomaineModal{{ domaine.id }}" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal pour confirmer la suppression -->
                                        <div class="modal fade" id="deleteDomaineModal{{ domaine.id }}" tabindex="-1" aria-labelledby="deleteDomaineModalLabel{{ domaine.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title" id="deleteDomaineModalLabel{{ domaine.id }}">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>Confirmer la suppression
                                                        </h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>Êtes-vous sûr de vouloir supprimer le domaine <strong>{{ domaine.nom }}</strong> ?</p>
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                                            <strong>Attention:</strong> Cette action supprimera également tous les thèmes associés à ce domaine.
                                                        </div>
                                                        <p class="text-muted">Cette action est irréversible.</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                            <i class="fas fa-times me-2"></i>Annuler
                                                        </button>
                                                        <a href="{{ url_for('delete_domaine', id=domaine.id) }}" class="btn btn-danger">
                                                            <i class="fas fa-trash me-2"></i>Supprimer
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Liste des Thèmes</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th style="width: 10%;">ID</th>
                                    <th style="width: 35%;">Nom</th>
                                    <th style="width: 30%;">Domaine</th>
                                    <th style="width: 25%;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for theme in themes %}
                                <tr class="align-middle">
                                    <td><strong>{{ theme.id }}</strong></td>
                                    <td>
                                        <i class="fas fa-tag text-success me-2"></i>
                                        <strong>{{ theme.nom }}</strong>
                                    </td>
                                    <td>
                                        <i class="fas fa-folder text-primary me-2"></i>
                                        {{ theme.domaine.nom }}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_theme', id=theme.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteThemeModal{{ theme.id }}" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal pour confirmer la suppression -->
                                        <div class="modal fade" id="deleteThemeModal{{ theme.id }}" tabindex="-1" aria-labelledby="deleteThemeModalLabel{{ theme.id }}" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title" id="deleteThemeModalLabel{{ theme.id }}">
                                                            <i class="fas fa-exclamation-triangle me-2"></i>Confirmer la suppression
                                                        </h5>
                                                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-warning me-2"></i>
                                                            Êtes-vous sûr de vouloir supprimer le thème <strong>{{ theme.nom }}</strong> ?
                                                        </div>
                                                        <p class="text-muted">Cette action est irréversible.</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                            <i class="fas fa-times me-2"></i>Annuler
                                                        </button>
                                                        <a href="{{ url_for('delete_theme', id=theme.id) }}" class="btn btn-danger">
                                                            <i class="fas fa-trash me-2"></i>Supprimer
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='domaines_themes') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
