/* Styles pour les informations de l'entreprise */

/* Page d'affichage des informations */
.company-info-page {
    background-color: #f8f9fa;
}

.company-logo-display {
    max-height: 150px;
    max-width: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.color-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #dee2e6;
    display: inline-block;
    margin-right: 8px;
}

.company-status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* Formulaire d'édition */
.company-form-section {
    margin-bottom: 2rem;
}

.form-control-color {
    width: 100%;
    height: 38px;
    border-radius: 0.375rem;
}

.logo-preview {
    max-height: 100px;
    max-width: 100%;
    object-fit: contain;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    padding: 8px;
    background-color: #fff;
}

/* Styles pour l'impression */
@media print {
    .company-header {
        text-align: center;
        margin-bottom: 20px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
    }
    
    .company-name {
        font-size: 24px;
        font-weight: bold;
        margin: 0;
        color: #000;
    }
    
    .company-slogan {
        font-style: italic;
        margin: 5px 0;
        color: #666;
    }
    
    .company-address,
    .company-contact {
        font-size: 12px;
        margin: 2px 0;
        color: #333;
    }
    
    .company-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 10px;
        border-top: 1px solid #000;
        padding-top: 5px;
        background-color: #fff;
    }
    
    .company-legal {
        font-size: 8px;
        color: #666;
        margin-top: 5px;
    }
    
    .company-logo-print {
        max-height: 60px;
        max-width: 150px;
        object-fit: contain;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .company-info-cards {
        margin-bottom: 1rem;
    }
    
    .color-preview {
        width: 30px;
        height: 30px;
    }
    
    .company-logo-display {
        max-height: 100px;
    }
}

/* Animation pour les changements de couleur */
.color-preview {
    transition: all 0.3s ease;
}

.color-preview:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Styles pour les cartes d'information */
.info-card {
    transition: transform 0.2s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Styles pour les labels */
.info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.info-value {
    color: #212529;
    margin-bottom: 1rem;
}

/* Styles pour les icônes */
.section-icon {
    color: #007bff;
    margin-right: 0.5rem;
}

/* Styles pour les boutons d'action */
.action-buttons {
    position: sticky;
    top: 20px;
}

.btn-company-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-company-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

/* Styles pour les alertes */
.company-alert {
    border-left: 4px solid #007bff;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Styles pour les dividers */
.company-divider {
    height: 2px;
    background: linear-gradient(to right, #007bff, transparent);
    margin: 2rem 0;
    border: none;
}

/* Styles pour les tooltips personnalisés */
.company-tooltip {
    position: relative;
    cursor: help;
}

.company-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.company-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Styles pour les champs de formulaire améliorés */
.enhanced-form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.enhanced-form-group .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.enhanced-form-group .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Animation de chargement */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
