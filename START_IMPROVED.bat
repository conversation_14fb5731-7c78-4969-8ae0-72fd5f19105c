@echo off
chcp 65001 >nul
title نظام إدارة التكوين - إصدار محسن

cls
echo.
echo ============================================================
echo                🎯 نظام إدارة التكوين
echo                    إصدار محسن
echo ============================================================
echo.

REM التحقق من وجود Python
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo 💡 يرجى تثبيت Python من https://python.org
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python %%i متوفر
)

REM التحقق من وجود pip
echo 🔍 فحص pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    echo.
    pause
    exit /b 1
) else (
    echo ✅ pip متوفر
)

REM التحقق من وجود ملف المتطلبات
if not exist requirements.txt (
    echo ❌ ملف requirements.txt غير موجود
    echo.
    pause
    exit /b 1
)

REM تثبيت المتطلبات
echo 📦 فحص وتثبيت المتطلبات...
pip install -r requirements.txt --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ❌ خطأ في تثبيت المتطلبات
    echo 💡 جرب تشغيل: pip install -r requirements.txt
    echo.
    pause
    exit /b 1
) else (
    echo ✅ المتطلبات جاهزة
)

REM التحقق من قاعدة البيانات وإعادة إنشائها
echo 🔍 فحص وتحديث قاعدة البيانات...
echo 🔧 تحديث جداول قاعدة البيانات...
python init_db.py
if errorlevel 1 (
    echo ❌ خطأ في تحديث قاعدة البيانات
    pause
    exit /b 1
) else (
    echo ✅ قاعدة البيانات محدثة وجاهزة
)

echo.
echo ============================================================
echo 📋 معلومات الدخول:
echo    🔗 الرابط: http://127.0.0.1:8080
echo    👤 المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🚀 بدء تشغيل التطبيق...
echo 💡 للإيقاف: اضغط Ctrl+C في نافذة Python
echo ============================================================
echo.

REM تشغيل التطبيق
python run.py

echo.
echo ============================================================
echo 🛑 تم إيقاف التطبيق
echo ============================================================
pause
