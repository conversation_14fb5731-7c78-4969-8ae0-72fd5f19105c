// Script pour la validation des formulaires

document.addEventListener('DOMContentLoaded', function() {
    // Sélectionner le formulaire de connexion
    const loginForm = document.getElementById('loginForm');

    if (loginForm) {
        // Ajouter un écouteur d'événement pour la soumission du formulaire
        loginForm.addEventListener('submit', function(event) {
            // Récupérer les champs du formulaire
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            let isValid = true;

            // Réinitialiser les messages d'erreur
            clearValidationErrors();

            // Valider le nom d'utilisateur
            if (!usernameField.value.trim()) {
                showValidationError(usernameField, 'Veuillez saisir votre nom d\'utilisateur');
                isValid = false;
            }

            // Valider le mot de passe
            if (!passwordField.value.trim()) {
                showValidationError(passwordField, 'Veuillez saisir votre mot de passe');
                isValid = false;
            }

            // Si le formulaire n'est pas valide, empêcher la soumission
            if (!isValid) {
                event.preventDefault();
            }
        });
    }

    // Fonction pour afficher un message d'erreur
    function showValidationError(inputElement, message) {
        // Ajouter la classe d'erreur à l'élément input
        inputElement.classList.add('is-invalid');

        // Créer un élément div pour le message d'erreur
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;

        // Insérer le message d'erreur après l'élément input
        inputElement.parentNode.appendChild(errorDiv);
    }

    // Fonction pour effacer tous les messages d'erreur
    function clearValidationErrors() {
        // Supprimer toutes les classes d'erreur
        document.querySelectorAll('.is-invalid').forEach(function(element) {
            element.classList.remove('is-invalid');
        });

        // Supprimer tous les messages d'erreur
        document.querySelectorAll('.invalid-feedback').forEach(function(element) {
            element.remove();
        });
    }

    // Ajouter des écouteurs d'événements pour effacer les erreurs lors de la saisie
    document.querySelectorAll('.form-control').forEach(function(input) {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
            const feedback = this.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.remove();
            }
        });
    });
});
