{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-building"></i> Organismes</h1>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('new_organisme') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle"></i> Nouvel Organisme
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_organismes') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un organisme..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Organismes</h5>
        </div>
        <div class="card-body">
            {% if organismes %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Raison Sociale</th>
                                <th>Forme Juridique</th>
                                <th>Gérant</th>
                                <th>Ville</th>
                                <th>Téléphone</th>
                                <th>Fax</th>
                                <th>Patente</th>
                                <th>ID Fiscal</th>
                                <th>N° RC</th>
                                <th>Email</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for organisme in organismes %}
                                <tr>
                                    <td>{{ organisme.id }}</td>
                                    <td>{{ organisme.raison_sociale }}</td>
                                    <td>{{ organisme.forme_juridique }}</td>
                                    <td>{{ organisme.nom_prenom_gerant }}</td>
                                    <td>{{ organisme.ville }}</td>
                                    <td>{{ organisme.telephone }}</td>
                                    <td>{{ organisme.fax }}</td>
                                    <td>{{ organisme.patente }}</td>
                                    <td>{{ organisme.identifiant_fiscal }}</td>
                                    <td>{{ organisme.num_rc }}</td>
                                    <td>{{ organisme.email }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_organisme', id=organisme.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_organisme', id=organisme.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{{ url_for('delete_organisme', id=organisme.id) }}" class="btn btn-sm btn-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet organisme?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    Aucun organisme trouvé. <a href="{{ url_for('new_organisme') }}">Ajouter un nouvel organisme</a>.
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='organismes') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
