#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص قاعدة البيانات والمستخدمين
"""

import os
import sys
from app import create_app, db
from app.models import User

def check_database():
    """فحص حالة قاعدة البيانات"""
    app = create_app()
    
    with app.app_context():
        try:
            print("🔍 فحص قاعدة البيانات...")
            
            # فحص الجداول
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📊 جداول قاعدة البيانات: {tables}")
            
            # فحص المستخدمين
            users = User.query.all()
            print(f"👥 عدد المستخدمين: {len(users)}")
            
            for user in users:
                print(f"  - المستخدم: {user.username}")
                print(f"    البريد: {user.email}")
                print(f"    مدير: {user.is_admin}")
                print(f"    كلمة المرور مشفرة: {'نعم' if user.password_hash else 'لا'}")
                
                # اختبار دالة check_password
                try:
                    test_result = user.check_password("admin123")
                    print(f"    اختبار كلمة المرور: {'نجح' if test_result else 'فشل'}")
                except Exception as e:
                    print(f"    خطأ في اختبار كلمة المرور: {e}")
                print()
            
            # إنشاء مستخدم تجريبي إذا لم يوجد
            if len(users) == 0:
                print("⚠️ لا يوجد مستخدمين! إنشاء مستخدم admin...")
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True,
                    nom_complet='المدير العام'
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم admin بكلمة المرور admin123")
            
        except Exception as e:
            print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_database()
