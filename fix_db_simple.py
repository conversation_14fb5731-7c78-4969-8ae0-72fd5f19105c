#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إصلاح قاعدة البيانات - حل بسيط
"""

import os
import sqlite3
from werkzeug.security import generate_password_hash

def fix_database():
    """إصلاح قاعدة البيانات مباشرة"""
    
    db_path = 'formations_central.db'
    
    try:
        print("🔍 فحص قاعدة البيانات...")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص جدول المستخدمين
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user';")
        user_table_exists = cursor.fetchone()
        
        if not user_table_exists:
            print("❌ جدول المستخدمين غير موجود!")
            conn.close()
            return
        
        print("✅ جدول المستخدمين موجود")
        
        # فحص المستخدمين الموجودين
        cursor.execute("SELECT id, username, email, password_hash FROM user;")
        users = cursor.fetchall()
        
        print(f"👥 عدد المستخدمين: {len(users)}")
        
        for user in users:
            print(f"  - ID: {user[0]}, المستخدم: {user[1]}, البريد: {user[2]}")
            print(f"    كلمة المرور: {'موجودة' if user[3] else 'مفقودة'}")
        
        # إذا لم يوجد مستخدم admin أو كلمة المرور فارغة
        admin_exists = False
        admin_id = None
        
        for user in users:
            if user[1] == 'admin':
                admin_exists = True
                admin_id = user[0]
                if not user[3]:  # كلمة المرور فارغة
                    print("⚠️ كلمة مرور admin فارغة - سيتم إصلاحها")
                break
        
        if not admin_exists:
            print("⚠️ مستخدم admin غير موجود - سيتم إنشاؤه")
            # إنشاء مستخدم admin جديد
            password_hash = generate_password_hash('admin123')
            cursor.execute("""
                INSERT INTO user (username, email, password_hash, is_admin, nom_complet)
                VALUES (?, ?, ?, ?, ?)
            """, ('admin', '<EMAIL>', password_hash, True, 'المدير العام'))
            print("✅ تم إنشاء مستخدم admin")
        else:
            # تحديث كلمة مرور admin
            password_hash = generate_password_hash('admin123')
            cursor.execute("""
                UPDATE user SET password_hash = ?, is_admin = 1 
                WHERE id = ?
            """, (password_hash, admin_id))
            print("✅ تم تحديث كلمة مرور admin")
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
        print("📋 معلومات الدخول:")
        print("   المستخدم: admin")
        print("   كلمة المرور: admin123")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_database()
