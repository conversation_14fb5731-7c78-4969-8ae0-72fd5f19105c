"""
Context processors pour rendre les données disponibles dans tous les templates
"""

def inject_company_info():
    """Injecte les informations de l'entreprise dans tous les templates"""
    try:
        from app.models import CompanyInfo
        company = CompanyInfo.get_active_company()
        return dict(company_info=company)
    except Exception as e:
        print(f"Erreur dans inject_company_info: {e}")
        return dict(company_info=None)

def inject_print_helpers():
    """Injecte des fonctions d'aide pour l'impression"""
    
    def get_company_header():
        """Retourne l'en-tête de l'entreprise pour l'impression"""
        try:
            from app.models import CompanyInfo
            company = CompanyInfo.get_active_company()
            if not company:
                return ""
        except Exception:
            return ""
        
        header_parts = []
        
        # Nom de l'entreprise
        if company.nom_entreprise:
            header_parts.append(f"<h2 class='company-name'>{company.nom_entreprise}</h2>")
        
        # Slogan
        if company.slogan:
            header_parts.append(f"<p class='company-slogan'>{company.slogan}</p>")
        
        # Adresse
        address = company.get_full_address()
        if address:
            header_parts.append(f"<p class='company-address'>{address}</p>")
        
        # Contact
        contact_info = company.get_contact_info()
        contact_parts = []
        if contact_info.get('telephone_1'):
            contact_parts.append(f"Tél: {contact_info['telephone_1']}")
        if contact_info.get('email'):
            contact_parts.append(f"Email: {contact_info['email']}")
        if contact_info.get('site_web'):
            contact_parts.append(f"Web: {contact_info['site_web']}")
        
        if contact_parts:
            header_parts.append(f"<p class='company-contact'>{' | '.join(contact_parts)}</p>")
        
        return ''.join(header_parts)
    
    def get_company_footer():
        """Retourne le pied de page de l'entreprise pour l'impression"""
        try:
            from app.models import CompanyInfo
            company = CompanyInfo.get_active_company()
            if not company:
                return ""
        except Exception:
            return ""
        
        footer_parts = []
        
        # Pied de page personnalisé
        if company.pied_de_page:
            footer_parts.append(f"<div class='company-footer'>{company.pied_de_page.replace(chr(10), '<br>')}</div>")
        
        # Mentions légales
        if company.mentions_legales:
            footer_parts.append(f"<div class='company-legal'>{company.mentions_legales.replace(chr(10), '<br>')}</div>")
        
        return ''.join(footer_parts)
    
    def get_company_logo_url():
        """Retourne l'URL du logo de l'entreprise"""
        try:
            from app.models import CompanyInfo
            company = CompanyInfo.get_active_company()
            if company and company.logo_path:
                return f"/static/{company.logo_path}"
            return None
        except Exception:
            return None
    
    def get_company_colors():
        """Retourne les couleurs de l'entreprise"""
        try:
            from app.models import CompanyInfo
            company = CompanyInfo.get_active_company()
            if company:
                return {
                    'primary': company.couleur_principale or '#007bff',
                    'secondary': company.couleur_secondaire or '#6c757d'
                }
            return {
                'primary': '#007bff',
                'secondary': '#6c757d'
            }
        except Exception:
            return {
                'primary': '#007bff',
                'secondary': '#6c757d'
            }
    
    return dict(
        get_company_header=get_company_header,
        get_company_footer=get_company_footer,
        get_company_logo_url=get_company_logo_url,
        get_company_colors=get_company_colors
    )
