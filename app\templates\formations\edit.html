{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-users"></i> Modifier Formation</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations de la Formation</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.theme.label(class="form-control-label") }}
                            {{ form.theme(class="form-control") }}
                            {% if form.theme.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.theme.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date.label(class="form-control-label") }}
                            {{ form.date(class="form-control", type="date") }}
                            {% if form.date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.organisme_id.label(class="form-control-label") }}
                            {{ form.organisme_id(class="form-control") }}
                            {% if form.organisme_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.organisme_id.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.piece_jointe.label(class="form-control-label") }}
                            {{ form.piece_jointe(class="form-control-file") }}
                            {% if form.piece_jointe.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.piece_jointe.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Formats acceptés: PDF, PNG, JPG, JPEG</small>
                            {% if formation.piece_jointe %}
                                <div class="mt-2">
                                    <p>Fichier actuel: <a href="{{ url_for('static', filename='uploads/' + formation.piece_jointe) }}" target="_blank">{{ formation.piece_jointe }}</a></p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('formations') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
