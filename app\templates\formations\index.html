{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-users"></i> Formations</h1>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('new_formation') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle Formation
            </a>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Thème</th>
                                <th>Organisme</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if formations %}
                                {% for formation in formations %}
                                    <tr>
                                        <td>{{ formation.id }}</td>
                                        <td>{{ formation.theme }}</td>
                                        <td>{{ formation.organisme.raison_sociale if formation.organisme else '-' }}</td>
                                        <td>{{ formation.date.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('edit_formation', id=formation.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('delete_formation', id=formation.id) }}" class="btn btn-sm btn-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette formation?');">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">Aucune formation trouvée</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='formations') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
