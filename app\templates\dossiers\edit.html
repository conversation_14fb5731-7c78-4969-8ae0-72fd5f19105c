{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-folder"></i> Modifier Dossier Technique</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations du Dossier Technique</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.fiche_inscription.label(class="form-control-label") }}
                            {{ form.fiche_inscription(class="form-control") }}
                            {% if form.fiche_inscription.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.fiche_inscription.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.domaine.label(class="form-control-label") }}
                            {{ form.domaine(class="form-control") }}
                            {% if form.domaine.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.domaine.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.theme.label(class="form-control-label") }}
                            {{ form.theme(class="form-control") }}
                            {% if form.theme.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.theme.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.organisme_formation.label(class="form-control-label") }}
                            {{ form.organisme_formation(class="form-control") }}
                            {% if form.organisme_formation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.organisme_formation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            {{ form.objectif.label(class="form-control-label") }}
                            {{ form.objectif(class="form-control", rows=3) }}
                            {% if form.objectif.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.objectif.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            {{ form.contenu_indicatif.label(class="form-control-label") }}
                            {{ form.contenu_indicatif(class="form-control", rows=3) }}
                            {% if form.contenu_indicatif.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.contenu_indicatif.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.type_formation.label(class="form-control-label") }}
                            {{ form.type_formation(class="form-control") }}
                            {% if form.type_formation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.type_formation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.cout_formation_ht.label(class="form-control-label") }}
                            {{ form.cout_formation_ht(class="form-control") }}
                            {% if form.cout_formation_ht.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.cout_formation_ht.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.nombre_cadres.label(class="form-control-label") }}
                            {{ form.nombre_cadres(class="form-control") }}
                            {% if form.nombre_cadres.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nombre_cadres.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.nombre_employes.label(class="form-control-label") }}
                            {{ form.nombre_employes(class="form-control") }}
                            {% if form.nombre_employes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nombre_employes.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.nombre_ouvriers.label(class="form-control-label") }}
                            {{ form.nombre_ouvriers(class="form-control") }}
                            {% if form.nombre_ouvriers.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nombre_ouvriers.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.conforme(class="form-check-input") }}
                            {{ form.conforme.label(class="form-check-label") }}
                            {% if form.conforme.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.conforme.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.depot_physique(class="form-check-input") }}
                            {{ form.depot_physique.label(class="form-check-label") }}
                            {% if form.depot_physique.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.depot_physique.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date_depot.label(class="form-control-label") }}
                            {{ form.date_depot(class="form-control", type="date") }}
                            {% if form.date_depot.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date_depot.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.validation(class="form-check-input") }}
                            {{ form.validation.label(class="form-check-label") }}
                            {% if form.validation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.validation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    {{ form.piece_jointe.label(class="form-control-label") }}
                    {{ form.piece_jointe(class="form-control-file") }}
                    {% if form.piece_jointe.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.piece_jointe.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-text text-muted">Formats acceptés: PDF, PNG, JPG, JPEG</small>
                    {% if dossier.piece_jointe %}
                        <div class="mt-2">
                            <p>Fichier actuel: <a href="{{ url_for('static', filename='uploads/' + dossier.piece_jointe) }}" target="_blank">{{ dossier.piece_jointe }}</a></p>
                        </div>
                    {% endif %}
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('dossiers_techniques') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
