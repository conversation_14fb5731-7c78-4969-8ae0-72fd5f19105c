{% extends "print_layout.html" %}

{% block content %}
<div class="info-section">
    <h2>Informations du Rendez-vous</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Formateur:</span>
                <span>{{ agenda.formateur.nom_prenom }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Date du Rendez-vous:</span>
                <span>{{ agenda.date_rendezvous.strftime('%d/%m/%Y') }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Description:</span>
                <span>{{ agenda.description }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">ID:</span>
                <span>{{ agenda.id }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations du Formateur</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Nom et Prénom:</span>
                <span>{{ agenda.formateur.nom_prenom }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Spécialité:</span>
                <span>{{ agenda.formateur.specialite }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Téléphone:</span>
                <span>{{ agenda.formateur.num_tel }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span>{{ agenda.formateur.email }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Adresse:</span>
                <span>{{ agenda.formateur.adresse }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Ville:</span>
                <span>{{ agenda.formateur.ville }}</span>
            </div>
        </div>
    </div>
</div>

{% if agenda.formateur.agenda|length > 1 %}
<div class="info-section">
    <h2>Autres Rendez-vous du Formateur</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Date</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            {% for rdv in agenda.formateur.agenda %}
                {% if rdv.id != agenda.id %}
                <tr>
                    <td>{{ rdv.date_rendezvous.strftime('%d/%m/%Y') }}</td>
                    <td>{{ rdv.description }}</td>
                </tr>
                {% endif %}
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}
{% endblock %}
