{% extends "print_layout.html" %}

{% block content %}
<div class="info-section">
    <h2>Informations Personnelles</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Nom et Prénom:</span>
                <span>{{ formateur.nom_prenom }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">CIN:</span>
                <span>{{ formateur.cin }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Date de Naissance:</span>
                <span>{{ formateur.date_naissance.strftime('%d/%m/%Y') if formateur.date_naissance else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Nationalité:</span>
                <span>{{ formateur.nationalite }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Adresse:</span>
                <span>{{ formateur.adresse }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Ville:</span>
                <span>{{ formateur.ville }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Téléphone:</span>
                <span>{{ formateur.num_tel }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span>{{ formateur.email }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Professionnelles</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Profession:</span>
                <span>{{ formateur.profession }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Spécialité:</span>
                <span>{{ formateur.specialite }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Niveau d'Études:</span>
                <span>{{ formateur.niveau_etudes }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Expérience (années):</span>
                <span>{{ formateur.experience }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Organisme:</span>
                <span>{{ formateur.organisme.raison_sociale if formateur.organisme else 'Indépendant' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Tarif Journalier:</span>
                <span>{{ formateur.tarif_journalier }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Bancaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Banque:</span>
                <span>{{ formateur.banque }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Agence:</span>
                <span>{{ formateur.agence }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">RIB:</span>
                <span>{{ formateur.rib }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Swift:</span>
                <span>{{ formateur.swift }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Complémentaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Date de Création:</span>
                <span>{{ formateur.date_creation.strftime('%d/%m/%Y') if formateur.date_creation else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Actif:</span>
                <span>{{ 'Oui' if formateur.actif else 'Non' }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Créé par:</span>
                <span>{{ formateur.user.nom_complet if formateur.user else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Notes:</span>
                <span>{{ formateur.notes }}</span>
            </div>
        </div>
    </div>
</div>

{% if formateur.dossiers_techniques %}
<div class="info-section">
    <h2>Dossiers Techniques Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Entreprise</th>
                <th>Thème</th>
                <th>Date de Début</th>
                <th>Coût HT</th>
            </tr>
        </thead>
        <tbody>
            {% for dossier in formateur.dossiers_techniques %}
            <tr>
                <td>{{ dossier.id }}</td>
                <td>{{ dossier.fiche_inscription.raison_sociale if dossier.fiche_inscription else 'N/A' }}</td>
                <td>{{ dossier.theme.nom if dossier.theme else 'N/A' }}</td>
                <td>{{ dossier.date_debut.strftime('%d/%m/%Y') if dossier.date_debut else 'N/A' }}</td>
                <td>{{ dossier.cout_formation_ht }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

{% if formateur.dossiers_remboursement %}
<div class="info-section">
    <h2>Dossiers de Remboursement Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Entreprise</th>
                <th>Thème</th>
                <th>Date</th>
                <th>Montant</th>
            </tr>
        </thead>
        <tbody>
            {% for remboursement in formateur.dossiers_remboursement %}
            <tr>
                <td>{{ remboursement.id }}</td>
                <td>{{ remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else 'N/A' }}</td>
                <td>{{ remboursement.theme }}</td>
                <td>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else 'N/A' }}</td>
                <td>{{ remboursement.montant }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

{% if formateur.agenda %}
<div class="info-section">
    <h2>Agenda du Formateur</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Date</th>
                <th>Description</th>
            </tr>
        </thead>
        <tbody>
            {% for rendez_vous in formateur.agenda %}
            <tr>
                <td>{{ rendez_vous.date_rendezvous.strftime('%d/%m/%Y') if rendez_vous.date_rendezvous else 'N/A' }}</td>
                <td>{{ rendez_vous.description }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}
{% endblock %}
