@echo off
chcp 65001 >nul
title نظام إدارة التكوين

cls
echo.
echo ============================================================
echo                نظام إدارة التكوين
echo ============================================================
echo.

REM إنهاء أي عمليات سابقة
taskkill /f /im GestionFormation.exe >nul 2>&1
taskkill /f /im python.exe >nul 2>&1

echo تشغيل البرنامج...
echo.

REM إنشاء قاعدة البيانات إذا لم تكن موجودة
if not exist "formations_central.db" (
    echo إنشاء قاعدة البيانات...
    python init_db.py
    if %errorlevel% neq 0 (
        echo فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)

echo معلومات الدخول:
echo الرابط: http://127.0.0.1:8080
echo المستخدم: admin
echo كلمة المرور: admin123
echo.
echo ============================================================

REM تشغيل التطبيق
python run.py

pause
