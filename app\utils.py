import os
import shutil
import sqlite3
import zipfile
import threading
import time
from datetime import datetime, timezone, timedelta
from flask import request, current_app
from flask_login import current_user
from app import db
from app.models import JournalActivite, SauvegardeDB

def enregistrer_activite(type_operation, description=None):
    """
    Enregistre une activité utilisateur dans le journal

    Args:
        type_operation (str): Type d'opération (connexion, création, modification, etc.)
        description (str): Description détaillée de l'opération
    """
    try:
        if current_user.is_authenticated:
            activite = JournalActivite(
                nom_utilisateur=current_user.username,
                type_operation=type_operation,
                description=description or f"{type_operation} effectuée par {current_user.username}",
                adresse_ip=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None,
                user_id=current_user.id
            )
            db.session.add(activite)
            db.session.commit()
    except Exception as e:
        print(f"Erreur lors de l'enregistrement de l'activité: {e}")

def creer_sauvegarde_db(description=None, type_sauvegarde='manuelle'):
    """
    Crée une sauvegarde de la base de données
    
    Args:
        description (str): Description de la sauvegarde
        type_sauvegarde (str): 'manuelle' ou 'automatique'
    
    Returns:
        tuple: (success, message, sauvegarde_obj)
    """
    try:
        # Créer le dossier de sauvegardes s'il n'existe pas
        backup_dir = os.path.join(current_app.root_path, '..', 'sauvegardes')
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        # Générer le nom du fichier de sauvegarde
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        nom_fichier = f"sauvegarde_{timestamp}.db"
        chemin_sauvegarde = os.path.join(backup_dir, nom_fichier)
        
        # Chemin de la base de données actuelle
        db_path = os.path.join(current_app.root_path, '..', 'app.db')
        
        # Copier la base de données
        shutil.copy2(db_path, chemin_sauvegarde)
        
        # Obtenir la taille du fichier
        taille_fichier = os.path.getsize(chemin_sauvegarde)
        
        # Enregistrer dans la base de données
        sauvegarde = SauvegardeDB(
            nom_fichier=nom_fichier,
            chemin_fichier=chemin_sauvegarde,
            taille_fichier=taille_fichier,
            type_sauvegarde=type_sauvegarde,
            description=description or f"Sauvegarde {type_sauvegarde} créée le {datetime.now().strftime('%d/%m/%Y à %H:%M')}",
            statut='réussie',
            user_id=current_user.id if current_user.is_authenticated else None
        )
        
        db.session.add(sauvegarde)
        db.session.commit()
        
        # Enregistrer l'activité
        enregistrer_activite('sauvegarde', f"Sauvegarde {type_sauvegarde} créée: {nom_fichier}")
        
        return True, f"Sauvegarde créée avec succès: {nom_fichier}", sauvegarde
        
    except Exception as e:
        return False, f"Erreur lors de la création de la sauvegarde: {str(e)}", None

def restaurer_sauvegarde_db(chemin_fichier_sauvegarde):
    """
    Restaure la base de données à partir d'une sauvegarde

    Args:
        chemin_fichier_sauvegarde (str): Chemin vers le fichier de sauvegarde

    Returns:
        tuple: (success, message)
    """
    backup_security_path = None
    try:
        # Vérifier que le fichier de sauvegarde existe
        if not os.path.exists(chemin_fichier_sauvegarde):
            return False, "Le fichier de sauvegarde n'existe pas"

        # Chemin de la base de données actuelle
        db_path = os.path.join(current_app.root_path, '..', 'app.db')
        db_path = os.path.abspath(db_path)

        # Vérifier que le fichier de sauvegarde est valide (SQLite)
        try:
            import sqlite3
            conn = sqlite3.connect(chemin_fichier_sauvegarde)
            conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
            conn.close()
        except Exception as e:
            return False, f"Le fichier de sauvegarde n'est pas une base de données SQLite valide: {str(e)}"

        # Créer une sauvegarde de sécurité avant la restauration
        backup_security_path = db_path + '.backup_before_restore'
        shutil.copy2(db_path, backup_security_path)

        # Fermer toutes les connexions à la base de données
        try:
            db.session.close()
            db.engine.dispose()
        except:
            pass

        # Attendre un peu pour s'assurer que les connexions sont fermées
        import time
        time.sleep(1)

        # Restaurer la sauvegarde
        shutil.copy2(chemin_fichier_sauvegarde, db_path)

        # Recréer les connexions
        try:
            from app import create_app
            app = create_app()
            with app.app_context():
                db.create_all()
        except:
            pass

        return True, "Base de données restaurée avec succès. Veuillez vous reconnecter."

    except Exception as e:
        # En cas d'erreur, essayer de restaurer la sauvegarde de sécurité
        try:
            if backup_security_path and os.path.exists(backup_security_path):
                shutil.copy2(backup_security_path, db_path)
        except:
            pass

        return False, f"Erreur lors de la restauration: {str(e)}"

def nettoyer_anciennes_sauvegardes(nombre_a_garder=10):
    """
    Supprime les anciennes sauvegardes en gardant seulement les plus récentes
    
    Args:
        nombre_a_garder (int): Nombre de sauvegardes à conserver
    """
    try:
        # Récupérer toutes les sauvegardes triées par date
        sauvegardes = SauvegardeDB.query.order_by(SauvegardeDB.date_creation.desc()).all()
        
        # Supprimer les sauvegardes en excès
        if len(sauvegardes) > nombre_a_garder:
            sauvegardes_a_supprimer = sauvegardes[nombre_a_garder:]
            
            for sauvegarde in sauvegardes_a_supprimer:
                # Supprimer le fichier physique
                if os.path.exists(sauvegarde.chemin_fichier):
                    os.remove(sauvegarde.chemin_fichier)
                
                # Supprimer l'enregistrement de la base de données
                db.session.delete(sauvegarde)
            
            db.session.commit()
            
            return True, f"{len(sauvegardes_a_supprimer)} anciennes sauvegardes supprimées"
        
        return True, "Aucune sauvegarde à supprimer"
        
    except Exception as e:
        return False, f"Erreur lors du nettoyage: {str(e)}"

def formater_taille_fichier(taille_octets):
    """
    Formate la taille d'un fichier en unités lisibles
    
    Args:
        taille_octets (int): Taille en octets
    
    Returns:
        str: Taille formatée (ex: "1.5 MB")
    """
    if taille_octets is None:
        return "Inconnue"
    
    for unite in ['octets', 'KB', 'MB', 'GB']:
        if taille_octets < 1024.0:
            return f"{taille_octets:.1f} {unite}"
        taille_octets /= 1024.0
    return f"{taille_octets:.1f} TB"

def obtenir_statistiques_journal():
    """
    Obtient des statistiques sur le journal d'activité
    
    Returns:
        dict: Statistiques du journal
    """
    try:
        total_activites = JournalActivite.query.count()
        activites_aujourd_hui = JournalActivite.query.filter(
            JournalActivite.date_operation >= datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        ).count()
        
        # Top 5 des types d'opérations
        from sqlalchemy import func
        top_operations = db.session.query(
            JournalActivite.type_operation,
            func.count(JournalActivite.id).label('count')
        ).group_by(JournalActivite.type_operation).order_by(func.count(JournalActivite.id).desc()).limit(5).all()
        
        # Top 5 des utilisateurs les plus actifs
        top_utilisateurs = db.session.query(
            JournalActivite.nom_utilisateur,
            func.count(JournalActivite.id).label('count')
        ).group_by(JournalActivite.nom_utilisateur).order_by(func.count(JournalActivite.id).desc()).limit(5).all()
        
        return {
            'total_activites': total_activites,
            'activites_aujourd_hui': activites_aujourd_hui,
            'top_operations': top_operations,
            'top_utilisateurs': top_utilisateurs
        }

    except Exception as e:
        print(f"Erreur lors de l'obtention des statistiques: {e}")
        return {
            'total_activites': 0,
            'activites_aujourd_hui': 0,
            'top_operations': [],
            'top_utilisateurs': []
        }

# Variables globales pour le planificateur
scheduler_thread = None
scheduler_running = False
heure_sauvegarde = "02:00"

def sauvegarde_automatique_job():
    """
    Fonction exécutée par le planificateur pour créer une sauvegarde automatique
    """
    try:
        from app import create_app
        app = create_app()

        with app.app_context():
            success, message, sauvegarde = creer_sauvegarde_db(
                type_sauvegarde='automatique',
                description=f'Sauvegarde automatique du {datetime.now().strftime("%d/%m/%Y à %H:%M")}'
            )

            if success:
                print(f"✅ Sauvegarde automatique créée: {message}")
                # Enregistrer l'activité
                enregistrer_activite('sauvegarde_auto', f'Sauvegarde automatique créée: {sauvegarde.nom_fichier}')

                # Nettoyer les anciennes sauvegardes automatiques (garder les 7 dernières)
                nettoyer_anciennes_sauvegardes_auto()
            else:
                print(f"❌ Erreur lors de la sauvegarde automatique: {message}")

    except Exception as e:
        print(f"❌ Erreur dans la sauvegarde automatique: {str(e)}")

def nettoyer_anciennes_sauvegardes_auto(nombre_a_garder=7):
    """
    Nettoie les anciennes sauvegardes automatiques

    Args:
        nombre_a_garder (int): Nombre de sauvegardes automatiques à conserver
    """
    try:
        # Récupérer les sauvegardes automatiques triées par date (plus récentes en premier)
        sauvegardes_auto = SauvegardeDB.query.filter_by(
            type_sauvegarde='automatique'
        ).order_by(SauvegardeDB.date_creation.desc()).all()

        # Supprimer les sauvegardes en excès
        if len(sauvegardes_auto) > nombre_a_garder:
            for sauvegarde in sauvegardes_auto[nombre_a_garder:]:
                try:
                    # Supprimer le fichier physique
                    chemin_fichier = os.path.join(
                        current_app.root_path, '..', 'sauvegardes', sauvegarde.nom_fichier
                    )
                    if os.path.exists(chemin_fichier):
                        os.remove(chemin_fichier)

                    # Supprimer l'enregistrement de la base
                    db.session.delete(sauvegarde)

                except Exception as e:
                    print(f"Erreur lors de la suppression de {sauvegarde.nom_fichier}: {e}")

            db.session.commit()
            print(f"🧹 Nettoyage terminé: {len(sauvegardes_auto) - nombre_a_garder} anciennes sauvegardes supprimées")

    except Exception as e:
        print(f"Erreur lors du nettoyage des sauvegardes: {e}")

def calculer_prochaine_execution(heure_str):
    """
    Calcule la prochaine exécution basée sur l'heure donnée

    Args:
        heure_str (str): Heure au format "HH:MM"

    Returns:
        datetime: Prochaine exécution
    """
    try:
        heure, minute = map(int, heure_str.split(':'))
        maintenant = datetime.now()
        prochaine = maintenant.replace(hour=heure, minute=minute, second=0, microsecond=0)

        # Si l'heure est déjà passée aujourd'hui, programmer pour demain
        if prochaine <= maintenant:
            prochaine += timedelta(days=1)

        return prochaine
    except:
        return None

def demarrer_planificateur_sauvegardes(heure="02:00"):
    """
    Démarre le planificateur de sauvegardes automatiques

    Args:
        heure (str): Heure de la sauvegarde au format "HH:MM" (défaut: 02:00)
    """
    global scheduler_thread, scheduler_running, heure_sauvegarde

    if scheduler_running:
        print("⚠️ Le planificateur de sauvegardes est déjà en cours d'exécution")
        return

    heure_sauvegarde = heure

    def run_scheduler():
        global scheduler_running
        scheduler_running = True

        print(f"🕐 Planificateur de sauvegardes démarré - Sauvegarde quotidienne à {heure}")

        while scheduler_running:
            try:
                maintenant = datetime.now()
                prochaine_execution = calculer_prochaine_execution(heure_sauvegarde)

                if prochaine_execution and maintenant >= prochaine_execution:
                    # Il est temps d'exécuter la sauvegarde
                    sauvegarde_automatique_job()
                    # Attendre 61 secondes pour éviter de relancer immédiatement
                    time.sleep(61)
                else:
                    # Vérifier toutes les minutes
                    time.sleep(60)

            except Exception as e:
                print(f"Erreur dans le planificateur: {e}")
                time.sleep(60)

    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()

def arreter_planificateur_sauvegardes():
    """
    Arrête le planificateur de sauvegardes automatiques
    """
    global scheduler_running
    scheduler_running = False
    print("🛑 Planificateur de sauvegardes arrêté")

def obtenir_statut_planificateur():
    """
    Obtient le statut du planificateur de sauvegardes

    Returns:
        dict: Statut du planificateur
    """
    global scheduler_running, heure_sauvegarde

    prochaine_execution = None
    if scheduler_running:
        prochaine_execution = calculer_prochaine_execution(heure_sauvegarde)

    return {
        'actif': scheduler_running,
        'prochaine_execution': prochaine_execution,
        'nombre_jobs': 1 if scheduler_running else 0
    }
