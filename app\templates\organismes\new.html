{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-building"></i> Nouvel Organisme</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations de l'Organisme</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.raison_sociale.label(class="form-control-label") }}
                            {{ form.raison_sociale(class="form-control") }}
                            {% if form.raison_sociale.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.raison_sociale.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.forme_juridique.label(class="form-control-label") }}
                            {{ form.forme_juridique(class="form-control") }}
                            {% if form.forme_juridique.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.forme_juridique.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.nom_prenom_gerant.label(class="form-control-label") }}
                            {{ form.nom_prenom_gerant(class="form-control") }}
                            {% if form.nom_prenom_gerant.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nom_prenom_gerant.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date_creation.label(class="form-control-label") }}
                            {{ form.date_creation(class="form-control", type="date") }}
                            {% if form.date_creation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date_creation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.adresse.label(class="form-control-label") }}
                            {{ form.adresse(class="form-control") }}
                            {% if form.adresse.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.adresse.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.ville.label(class="form-control-label") }}
                            {{ form.ville(class="form-control") }}
                            {% if form.ville.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ville.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.telephone.label(class="form-control-label") }}
                            {{ form.telephone(class="form-control") }}
                            {% if form.telephone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.telephone.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.fax.label(class="form-control-label") }}
                            {{ form.fax(class="form-control") }}
                            {% if form.fax.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.fax.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.patente.label(class="form-control-label") }}
                            {{ form.patente(class="form-control") }}
                            {% if form.patente.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.patente.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.identifiant_fiscal.label(class="form-control-label") }}
                            {{ form.identifiant_fiscal(class="form-control") }}
                            {% if form.identifiant_fiscal.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.identifiant_fiscal.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.num_rc.label(class="form-control-label") }}
                            {{ form.num_rc(class="form-control") }}
                            {% if form.num_rc.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.num_rc.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.num_cnss.label(class="form-control-label") }}
                            {{ form.num_cnss(class="form-control") }}
                            {% if form.num_cnss.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.num_cnss.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.piece_jointe.label(class="form-control-label") }}
                            {{ form.piece_jointe(class="form-control-file") }}
                            {% if form.piece_jointe.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.piece_jointe.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Formats acceptés: PDF, PNG, JPG, JPEG</small>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('organismes') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
