/**
 * Enhanced UI JavaScript - نظام إدارة التكوين
 * تحسينات تفاعلية للواجهة
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // تحسين الرسوم المتحركة للبطاقات
    function initCardAnimations() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('animate-fade-in');
        });
    }

    // تحسين الشريط الجانبي للشاشات الصغيرة
    function initSidebarToggle() {
        const sidebar = document.getElementById('sidebar-wrapper');
        const toggleBtn = document.createElement('button');
        toggleBtn.className = 'sidebar-toggle btn btn-primary';
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        
        // إضافة الزر للشاشات الصغيرة فقط
        if (window.innerWidth <= 992) {
            document.body.appendChild(toggleBtn);
            
            toggleBtn.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
            
            // إغلاق الشريط الجانبي عند النقر خارجه
            document.addEventListener('click', function(e) {
                if (!sidebar.contains(e.target) && !toggleBtn.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }
    }

    // تحسين النماذج
    function initFormEnhancements() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            
            inputs.forEach(input => {
                // إضافة تأثيرات التركيز
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
                
                // تحسين التحقق من صحة البيانات
                input.addEventListener('input', function() {
                    if (this.checkValidity()) {
                        this.classList.remove('is-invalid');
                        this.classList.add('is-valid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                });
            });
        });
    }

    // تحسين الجداول
    function initTableEnhancements() {
        const tables = document.querySelectorAll('.table');
        tables.forEach(table => {
            // إضافة تأثيرات التمرير
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                    this.style.transition = 'transform 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    }

    // تحسين الأزرار
    function initButtonEnhancements() {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // إضافة تأثير الموجة
                const ripple = document.createElement('span');
                ripple.className = 'ripple';
                this.appendChild(ripple);
                
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // تحسين التنبيهات
    function initAlertEnhancements() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            // إضافة رسوم متحركة للظهور
            alert.classList.add('animate-slide-up');
            
            // إخفاء تلقائي بعد 5 ثوان
            if (!alert.classList.contains('alert-permanent')) {
                setTimeout(() => {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-20px)';
                    setTimeout(() => {
                        alert.remove();
                    }, 300);
                }, 5000);
            }
        });
    }

    // تحسين البحث المباشر
    function initLiveSearch() {
        const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', function() {
                clearTimeout(timeout);
                const query = this.value.toLowerCase();
                
                timeout = setTimeout(() => {
                    const targetTable = document.querySelector('.table tbody');
                    if (targetTable) {
                        const rows = targetTable.querySelectorAll('tr');
                        rows.forEach(row => {
                            const text = row.textContent.toLowerCase();
                            if (text.includes(query) || query === '') {
                                row.style.display = '';
                                row.classList.add('animate-fade-in');
                            } else {
                                row.style.display = 'none';
                            }
                        });
                    }
                }, 300);
            });
        });
    }

    // تحسين التحميل
    function initLoadingStates() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function() {
                const submitBtn = this.querySelector('button[type="submit"], input[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
                }
            });
        });
    }

    // تحسين التوقيت
    function initTimeUpdates() {
        const timeElements = document.querySelectorAll('.time-update');
        timeElements.forEach(element => {
            setInterval(() => {
                const now = new Date();
                element.textContent = now.toLocaleString('ar-MA');
            }, 1000);
        });
    }

    // تحسين الإحصائيات المتحركة
    function initAnimatedCounters() {
        const counters = document.querySelectorAll('.counter');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            let current = 0;
            const increment = target / 50;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current);
            }, 50);
        });
    }

    // تحسين التمرير السلس
    function initSmoothScrolling() {
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // تحسين الاستجابة للشاشات
    function initResponsiveEnhancements() {
        function handleResize() {
            const sidebar = document.getElementById('sidebar-wrapper');
            const toggleBtn = document.querySelector('.sidebar-toggle');
            
            if (window.innerWidth > 992) {
                if (sidebar) sidebar.classList.remove('show');
                if (toggleBtn) toggleBtn.style.display = 'none';
            } else {
                if (toggleBtn) toggleBtn.style.display = 'block';
            }
        }
        
        window.addEventListener('resize', handleResize);
        handleResize(); // تشغيل فوري
    }

    // تشغيل جميع التحسينات
    initCardAnimations();
    initSidebarToggle();
    initFormEnhancements();
    initTableEnhancements();
    initButtonEnhancements();
    initAlertEnhancements();
    initLiveSearch();
    initLoadingStates();
    initTimeUpdates();
    initAnimatedCounters();
    initSmoothScrolling();
    initResponsiveEnhancements();

    // إضافة CSS للتأثيرات
    const style = document.createElement('style');
    style.textContent = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .focused {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }
        
        .btn {
            position: relative;
            overflow: hidden;
        }
    `;
    document.head.appendChild(style);
});

// تحسين الأداء
window.addEventListener('load', function() {
    // إخفاء شاشة التحميل إذا كانت موجودة
    const loader = document.querySelector('.loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
            loader.remove();
        }, 500);
    }
    
    // تحسين الصور
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('load', function() {
            this.classList.add('animate-fade-in');
        });
    });
});
