/*
 * Enhanced Style - نظام إدارة التكوين
 * تصميم محسن يجمع أفضل العناصر من التصميمات السابقة
 */

/* Variables CSS للألوان والتصميم */
:root {
    /* الألوان الأساسية */
    --primary-color: #4e73df;
    --primary-dark: #3d5aa3;
    --secondary-color: #6c757d;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #343a40;
    --white: #ffffff;
    
    /* ألوان الخلفية والنص */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fc;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --text-primary: #333333;
    --text-secondary: #6c757d;
    --text-muted: #8e9bae;
    
    /* الحدود والظلال */
    --border-color: #e3e6f0;
    --border-radius: 0.35rem;
    --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --card-shadow-hover: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.25);
    
    /* الخطوط */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 0.875rem;
    --font-weight-normal: 400;
    --font-weight-bold: 600;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    margin: 0;
    padding: 0;
    transition: all 0.3s ease;
}

/* تحسين الروابط */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* تحسين البطاقات */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    margin-bottom: var(--spacing-lg);
}

.card:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.card-body {
    padding: var(--spacing-lg);
}

/* تحسين الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-bold);
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(78, 115, 223, 0.3);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

/* تحسين النماذج */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    outline: none;
}

.form-label {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* تحسين الجداول */
.table {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: var(--font-weight-bold);
    border: none;
    padding: var(--spacing-md);
}

.table td {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(78, 115, 223, 0.1);
}

/* تحسين التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    font-weight: var(--font-weight-normal);
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-warning {
    background-color: rgba(246, 194, 62, 0.1);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background-color: rgba(54, 185, 204, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* تحسين الشريط الجانبي */
.sidebar {
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
    min-height: 100vh;
}

.sidebar-heading {
    background: var(--bg-gradient);
    color: white;
    padding: var(--spacing-lg);
    font-weight: var(--font-weight-bold);
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.nav-link {
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: rgba(78, 115, 223, 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    transform: translateX(5px);
}

.nav-link.active {
    background-color: rgba(78, 115, 223, 0.15);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

/* تحسين صفحة تسجيل الدخول */
.login-container {
    min-height: 100vh;
    background: var(--bg-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.login-card {
    background-color: var(--bg-primary);
    border-radius: calc(var(--border-radius) * 2);
    box-shadow: var(--card-shadow-hover);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    min-height: 500px;
}

.login-form-container {
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.login-title {
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    font-size: 2rem;
}

.login-subtitle {
    color: var(--text-muted);
    margin-bottom: var(--spacing-xl);
}

.login-image-container {
    background: linear-gradient(135deg, rgba(78, 115, 223, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .login-card {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .login-image-container {
        display: none;
    }
    
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
}

/* تحسين الرسوم المتحركة */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* تحسين العناصر التفاعلية */
.interactive-element {
    transition: all 0.3s ease;
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
    box-shadow: var(--card-shadow-hover);
}

/* تحسين الأيقونات */
.icon {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
}

/* تحسين التباعد */
.mb-custom {
    margin-bottom: var(--spacing-lg);
}

.mt-custom {
    margin-top: var(--spacing-lg);
}

.p-custom {
    padding: var(--spacing-lg);
}

/* تحسين إضافي للبطاقات الإحصائية */
.stats-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: calc(var(--border-radius) * 2);
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* تحسين الشريط العلوي */
.top-bar {
    background: var(--bg-gradient);
    color: white;
    padding: var(--spacing-sm) 0;
    text-align: center;
    font-size: 0.8rem;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: 2.5rem;
    background: var(--bg-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تحسين الأيقونات في الشريط الجانبي */
.sidebar .nav-link i {
    width: 20px;
    text-align: center;
    margin-left: var(--spacing-sm);
}

/* تحسين المحتوى الرئيسي */
#page-content-wrapper {
    background-color: var(--bg-secondary);
    min-height: 100vh;
    padding: var(--spacing-lg);
}

/* تحسين الفوتر */
.footer {
    background-color: var(--bg-primary);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-lg) 0;
    margin-top: auto;
    text-align: center;
    color: var(--text-muted);
}

/* تحسين الشاشات الصغيرة */
@media (max-width: 992px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    #page-content-wrapper {
        margin-left: 0;
        width: 100%;
    }

    .sidebar-toggle {
        display: block;
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        padding: var(--spacing-sm);
        border-radius: var(--border-radius);
    }
}

@media (min-width: 993px) {
    .sidebar-toggle {
        display: none;
    }
}
