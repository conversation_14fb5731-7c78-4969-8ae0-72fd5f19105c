@echo off
chcp 65001 >nul
title نظام إدارة التكوين - الحل النهائي

cls
echo.
echo ============================================================
echo           🌐 نظام إدارة التكوين - الحل النهائي
echo ============================================================
echo.

echo 🔍 فحص الحلول المتاحة...
echo.

REM الحل الأول: استخدام الملف التنفيذي إذا كان متوفراً
if exist "dist\GestionFormation.exe" (
    echo ✅ الحل الأول: الملف التنفيذي متوفر
    echo 🚀 تشغيل الملف التنفيذي...
    echo.
    echo 📋 معلومات الدخول:
    echo    🔗 الرابط: http://127.0.0.1:8080
    echo    👤 المستخدم: admin
    echo    🔑 كلمة المرور: admin123
    echo.
    echo ============================================================
    
    start "" "dist\GestionFormation.exe"
    
    REM انتظار قليل ثم فتح المتصفح
    timeout /t 3 /nobreak >nul
    start "" "http://127.0.0.1:8080"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo 🌐 تم فتح المتصفح تلقائياً
    echo.
    goto :success
)

REM الحل الثاني: محاولة تشغيل Python
echo ⚠️ الملف التنفيذي غير متوفر
echo 🔧 محاولة الحل الثاني: Python...
echo.

REM البحث عن Python في المواقع الشائعة
set PYTHON_FOUND=0

REM فحص Python في PATH
python --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    goto :python_found
)

REM فحص py launcher
py --version >nul 2>&1
if %errorlevel% equ 0 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    goto :python_found
)

REM فحص Python 3.11 في المجلدات الشائعة
if exist "C:\Python311\python.exe" (
    set PYTHON_CMD=C:\Python311\python.exe
    set PYTHON_FOUND=1
    goto :python_found
)

if exist "C:\Python310\python.exe" (
    set PYTHON_CMD=C:\Python310\python.exe
    set PYTHON_FOUND=1
    goto :python_found
)

if exist "%LOCALAPPDATA%\Programs\Python\Python311\python.exe" (
    set PYTHON_CMD=%LOCALAPPDATA%\Programs\Python\Python311\python.exe
    set PYTHON_FOUND=1
    goto :python_found
)

if exist "%LOCALAPPDATA%\Programs\Python\Python310\python.exe" (
    set PYTHON_CMD=%LOCALAPPDATA%\Programs\Python\Python310\python.exe
    set PYTHON_FOUND=1
    goto :python_found
)

REM إذا لم يتم العثور على Python
if %PYTHON_FOUND% equ 0 (
    echo ❌ لم يتم العثور على Python
    echo.
    echo 💡 الحلول المتاحة:
    echo    1. تثبيت Python 3.11 من python.org
    echo    2. استخدام الملف التنفيذي (إذا كان متوفراً)
    echo.
    goto :end
)

:python_found
echo ✅ تم العثور على Python: %PYTHON_CMD%
echo 🔧 محاولة تشغيل البرنامج...
echo.

REM إنشاء قاعدة البيانات إذا لم تكن موجودة
if not exist "formations_central.db" (
    echo 🔧 إنشاء قاعدة البيانات...
    "%PYTHON_CMD%" init_db.py
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        goto :end
    )
)

echo 📋 معلومات الدخول:
echo    🔗 الرابط: http://127.0.0.1:8080
echo    👤 المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🚀 تشغيل البرنامج...
echo ============================================================

REM تشغيل التطبيق
"%PYTHON_CMD%" run.py

goto :success

:success
echo.
echo ============================================================
echo ✅ تم تشغيل البرنامج بنجاح!
echo ============================================================
goto :end

:end
echo.
echo ============================================================
echo 📋 ملخص الحلول:
echo ============================================================
echo 🎯 الحل الأمثل: استخدام الملف التنفيذي
echo    .\dist\GestionFormation.exe
echo.
echo 🔧 الحل البديل: Python + run.py
echo    python run.py
echo.
echo 🔗 رابط البرنامج: http://127.0.0.1:8080
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo ============================================================

pause
