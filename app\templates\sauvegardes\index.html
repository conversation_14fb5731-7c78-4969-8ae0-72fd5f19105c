{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-database me-2"></i>
                        Gestion des Sauvegardes
                    </h3>
                    <div>
                        <a href="{{ url_for('config_sauvegarde_auto') }}" class="btn btn-info">
                            <i class="fas fa-clock me-1"></i>
                            Sauvegardes Auto
                        </a>
                        <a href="{{ url_for('nouvelle_sauvegarde') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Nouvelle Sauvegarde
                        </a>
                        <a href="{{ url_for('restaurer_sauvegarde') }}" class="btn btn-warning">
                            <i class="fas fa-upload me-1"></i>
                            Restaurer
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if sauvegardes %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Nom du Fichier</th>
                                    <th>Type</th>
                                    <th>Taille</th>
                                    <th>Date de Création</th>
                                    <th>Statut</th>
                                    <th>Créé par</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sauvegarde in sauvegardes %}
                                <tr>
                                    <td>{{ sauvegarde.id }}</td>
                                    <td>
                                        <i class="fas fa-file-archive me-1"></i>
                                        {{ sauvegarde.nom_fichier }}
                                    </td>
                                    <td>
                                        {% if sauvegarde.type_sauvegarde == 'manuelle' %}
                                            <span class="badge bg-primary">Manuelle</span>
                                        {% else %}
                                            <span class="badge bg-info">Automatique</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ sauvegarde.taille_formatee }}</td>
                                    <td>{{ sauvegarde.date_creation.strftime('%d/%m/%Y %H:%M') }}</td>
                                    <td>
                                        {% if sauvegarde.statut == 'réussie' %}
                                            <span class="badge bg-success">Réussie</span>
                                        {% elif sauvegarde.statut == 'échouée' %}
                                            <span class="badge bg-danger">Échouée</span>
                                        {% else %}
                                            <span class="badge bg-warning">En cours</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ sauvegarde.user.username if sauvegarde.user else 'Système' }}</td>
                                    <td>
                                        <span class="text-muted" title="{{ sauvegarde.description }}">
                                            {{ sauvegarde.description[:50] + '...' if sauvegarde.description and sauvegarde.description|length > 50 else sauvegarde.description or 'Aucune description' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('telecharger_sauvegarde', id=sauvegarde.id) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Télécharger">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmerSuppression({{ sauvegarde.id }}, '{{ sauvegarde.nom_fichier }}')"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-database fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune sauvegarde trouvée</h5>
                        <p class="text-muted">Créez votre première sauvegarde pour sécuriser vos données.</p>
                        <a href="{{ url_for('nouvelle_sauvegarde') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Créer une Sauvegarde
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la Suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer la sauvegarde <strong id="nomFichier"></strong> ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action est irréversible.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Supprimer</a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerSuppression(id, nomFichier) {
    document.getElementById('nomFichier').textContent = nomFichier;
    document.getElementById('confirmDeleteBtn').href = "{{ url_for('supprimer_sauvegarde', id=0) }}".replace('0', id);
    new bootstrap.Modal(document.getElementById('confirmDeleteModal')).show();
}
</script>
{% endblock %}
