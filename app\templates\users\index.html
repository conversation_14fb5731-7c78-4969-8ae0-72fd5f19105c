{% extends "base.html" %}

{% block content %}
<style>
.text-purple {
    color: #6f42c1 !important;
}
.modal-lg {
    max-width: 900px;
}
.progress {
    height: 8px;
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-users"></i> Gestion des Utilisateurs</h1>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('new_user') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Ajouter un Utilisateur
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_users') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un utilisateur..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Utilisateurs</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 8%;">ID</th>
                            <th style="width: 18%;">Nom d'utilisateur</th>
                            <th style="width: 20%;">Email</th>
                            <th style="width: 18%;">Nom complet</th>
                            <th style="width: 10%;">Admin</th>
                            <th style="width: 12%;">Permissions</th>
                            <th style="width: 14%;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr class="align-middle">
                            <td><strong>{{ user.id }}</strong></td>
                            <td>
                                <i class="fas fa-user text-primary me-2"></i>
                                <strong>{{ user.username }}</strong>
                            </td>
                            <td>
                                <i class="fas fa-envelope text-info me-2"></i>
                                {{ user.email }}
                            </td>
                            <td>
                                <i class="fas fa-id-card text-secondary me-2"></i>
                                {{ user.nom_complet }}
                            </td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge bg-success">
                                    <i class="fas fa-crown me-1"></i>Oui
                                </span>
                                {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-user me-1"></i>Non
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#permissionsModal{{ user.id }}">
                                    <i class="fas fa-key me-1"></i>Voir
                                </button>

                                <!-- Modal pour afficher les permissions -->
                                <div class="modal fade" id="permissionsModal{{ user.id }}" tabindex="-1" aria-labelledby="permissionsModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header bg-info text-white">
                                                <h5 class="modal-title" id="permissionsModalLabel{{ user.id }}">
                                                    <i class="fas fa-key me-2"></i>Permissions de {{ user.username }}
                                                </h5>
                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <!-- Informations générales de l'utilisateur -->
                                                <div class="row mb-4">
                                                    <div class="col-md-6">
                                                        <div class="card border-primary">
                                                            <div class="card-header bg-primary text-white">
                                                                <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informations Utilisateur</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                <p class="mb-2"><strong>ID:</strong> {{ user.id }}</p>
                                                                <p class="mb-2"><strong>Nom d'utilisateur:</strong> {{ user.username }}</p>
                                                                <p class="mb-2"><strong>Email:</strong> {{ user.email }}</p>
                                                                <p class="mb-2"><strong>Nom complet:</strong> {{ user.nom_complet }}</p>
                                                                <p class="mb-0"><strong>Date de création:</strong>
                                                                    {% if user.date_creation %}
                                                                        {{ user.date_creation.strftime('%d/%m/%Y à %H:%M') }}
                                                                    {% else %}
                                                                        Non définie
                                                                    {% endif %}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="card border-info">
                                                            <div class="card-header bg-info text-white">
                                                                <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Statistiques</h6>
                                                            </div>
                                                            <div class="card-body">
                                                                {% set total_permissions = 11 %}
                                                                {% set active_permissions = (user.perm_fiche_inscription|int) + (user.perm_dossier_technique|int) + (user.perm_dossier_remboursement|int) + (user.perm_organisme|int) + (user.perm_formateur|int) + (user.perm_agenda|int) + (user.perm_domaine_theme|int) + (user.perm_rapports|int) + (user.perm_sauvegardes|int) + (user.perm_journal_activite|int) + (user.perm_gestion_utilisateurs|int) %}
                                                                <p class="mb-2"><strong>Permissions actives:</strong> {{ active_permissions }}/{{ total_permissions }}</p>
                                                                <div class="progress mb-2">
                                                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ (active_permissions/total_permissions*100)|round }}%"></div>
                                                                </div>
                                                                <p class="mb-2"><strong>Statut:</strong>
                                                                    {% if user.is_admin %}
                                                                        <span class="badge bg-warning text-dark"><i class="fas fa-crown me-1"></i>Administrateur</span>
                                                                    {% else %}
                                                                        <span class="badge bg-secondary"><i class="fas fa-user me-1"></i>Utilisateur Standard</span>
                                                                    {% endif %}
                                                                </p>
                                                                <p class="mb-0"><strong>Dernière connexion:</strong>
                                                                    {% if user.derniere_connexion %}
                                                                        {{ user.derniere_connexion.strftime('%d/%m/%Y à %H:%M') }}
                                                                    {% else %}
                                                                        Jamais connecté
                                                                    {% endif %}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Permissions détaillées -->
                                                <div class="card">
                                                    <div class="card-header bg-secondary text-white">
                                                        <h6 class="mb-0"><i class="fas fa-key me-2"></i>Permissions Détaillées</h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <ul class="list-group list-group-flush">
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-file-alt text-primary me-2"></i>
                                                                            <strong>Fiche d'inscription</strong>
                                                                            <br><small class="text-muted">Gestion des fiches d'inscription des formations</small>
                                                                        </div>
                                                                        {% if user.perm_fiche_inscription %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-folder-open text-info me-2"></i>
                                                                            <strong>Dossier technique</strong>
                                                                            <br><small class="text-muted">Accès aux dossiers techniques des formations</small>
                                                                        </div>
                                                                        {% if user.perm_dossier_technique %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-money-bill-wave text-success me-2"></i>
                                                                            <strong>Dossier de remboursement</strong>
                                                                            <br><small class="text-muted">Gestion des remboursements de formations</small>
                                                                        </div>
                                                                        {% if user.perm_dossier_remboursement %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-building text-warning me-2"></i>
                                                                            <strong>Organisme</strong>
                                                                            <br><small class="text-muted">Gestion des organismes de formation</small>
                                                                        </div>
                                                                        {% if user.perm_organisme %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <ul class="list-group list-group-flush">
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-chalkboard-teacher text-purple me-2"></i>
                                                                            <strong>Formateur</strong>
                                                                            <br><small class="text-muted">Gestion des formateurs et leurs informations</small>
                                                                        </div>
                                                                        {% if user.perm_formateur %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-calendar-alt text-danger me-2"></i>
                                                                            <strong>Agenda des formateurs</strong>
                                                                            <br><small class="text-muted">Planification et gestion des rendez-vous</small>
                                                                        </div>
                                                                        {% if user.perm_agenda %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-tags text-secondary me-2"></i>
                                                                            <strong>Domaines et Thèmes</strong>
                                                                            <br><small class="text-muted">Configuration des domaines et thèmes de formation</small>
                                                                        </div>
                                                                        {% if user.perm_domaine_theme %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                    <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                        <div>
                                                                            <i class="fas fa-users-cog text-dark me-2"></i>
                                                                            <strong>Gestion des utilisateurs</strong>
                                                                            <br><small class="text-muted">Gestion des comptes utilisateurs et permissions</small>
                                                                        </div>
                                                                        {% if user.perm_gestion_utilisateurs %}
                                                                        <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                        {% else %}
                                                                        <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                        {% endif %}
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>

                                                        <!-- Outils Système -->
                                                        <div class="mb-4">
                                                            <h6 class="text-warning border-bottom pb-2 mb-3">
                                                                <i class="fas fa-cogs me-2"></i>Outils Système
                                                            </h6>
                                                            <div class="row">
                                                                <div class="col-md-6">
                                                                    <ul class="list-group list-group-flush">
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                            <div>
                                                                                <i class="fas fa-chart-bar text-info me-2"></i>
                                                                                <strong>Rapports</strong>
                                                                                <br><small class="text-muted">Génération et consultation des rapports</small>
                                                                            </div>
                                                                            {% if user.perm_rapports %}
                                                                            <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                            {% else %}
                                                                            <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                            {% endif %}
                                                                        </li>
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                            <div>
                                                                                <i class="fas fa-database text-success me-2"></i>
                                                                                <strong>Sauvegardes</strong>
                                                                                <br><small class="text-muted">Gestion des sauvegardes de la base de données</small>
                                                                            </div>
                                                                            {% if user.perm_sauvegardes %}
                                                                            <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                            {% else %}
                                                                            <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                            {% endif %}
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                                <div class="col-md-6">
                                                                    <ul class="list-group list-group-flush">
                                                                        <li class="list-group-item d-flex justify-content-between align-items-center border-0 px-0">
                                                                            <div>
                                                                                <i class="fas fa-history text-warning me-2"></i>
                                                                                <strong>Journal d'activité</strong>
                                                                                <br><small class="text-muted">Consultation du journal des activités système</small>
                                                                            </div>
                                                                            {% if user.perm_journal_activite %}
                                                                            <span class="badge bg-success rounded-pill"><i class="fas fa-check me-1"></i>Autorisé</span>
                                                                            {% else %}
                                                                            <span class="badge bg-danger rounded-pill"><i class="fas fa-times me-1"></i>Refusé</span>
                                                                            {% endif %}
                                                                        </li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="fas fa-times me-2"></i>Fermer
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_user', id=user.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ user.id }}" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>

                                <!-- Modal pour confirmer la suppression -->
                                <div class="modal fade" id="deleteModal{{ user.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ user.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header bg-danger text-white">
                                                <h5 class="modal-title" id="deleteModalLabel{{ user.id }}">
                                                    <i class="fas fa-exclamation-triangle me-2"></i>Confirmer la suppression
                                                </h5>
                                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-warning me-2"></i>
                                                    Êtes-vous sûr de vouloir supprimer l'utilisateur <strong>{{ user.username }}</strong> ?
                                                </div>
                                                <p class="text-muted">Cette action est irréversible.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                    <i class="fas fa-times me-2"></i>Annuler
                                                </button>
                                                <a href="{{ url_for('delete_user', id=user.id) }}" class="btn btn-danger">
                                                    <i class="fas fa-trash me-2"></i>Supprimer
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='users') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
