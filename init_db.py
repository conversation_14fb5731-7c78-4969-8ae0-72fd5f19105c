#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء قاعدة البيانات الأساسية
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User

def init_database():
    """إنشاء قاعدة البيانات وإضافة المستخدم الافتراضي"""
    app = create_app()
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        
        # التحقق من وجود المستخدم الافتراضي
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            # إنشاء المستخدم الافتراضي
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                nom_complet='المدير العام',
                perm_fiche_inscription=True,
                perm_dossier_technique=True,
                perm_dossier_remboursement=True,
                perm_organisme=True,
                perm_formateur=True,
                perm_agenda=True,
                perm_domaine_theme=True,
                perm_rapports=True,
                perm_sauvegardes=True,
                perm_journal_activite=True,
                perm_gestion_utilisateurs=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("✅ تم إنشاء المستخدم الافتراضي: admin/admin123")
        else:
            print("✅ المستخدم الافتراضي موجود بالفعل")
        
        print("✅ تم إنشاء قاعدة البيانات بنجاح")

if __name__ == '__main__':
    init_database()
