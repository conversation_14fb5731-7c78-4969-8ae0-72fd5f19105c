{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-search"></i> Résultats de recherche</h1>
            <p class="text-muted">Résultats pour "{{ query }}" ({{ total_results }} résultats trouvés)</p>
        </div>
    </div>

    {% if not total_results %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> Aucun résultat trouvé pour "{{ query }}".
    </div>
    {% endif %}

    {% if fiches %}
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-file-alt"></i> Fiches d'inscription ({{ fiches|length }})</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Raison Sociale</th>
                            <th>Email</th>
                            <th>Téléphone</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for fiche in fiches %}
                        <tr>
                            <td>{{ fiche.id }}</td>
                            <td>{{ fiche.raison_sociale }}</td>
                            <td>{{ fiche.email }}</td>
                            <td>{{ fiche.tel_entreprise }}</td>
                            <td>
                                <a href="{{ url_for('edit_fiche_inscription', id=fiche.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if dossiers %}
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-folder"></i> Dossiers techniques ({{ dossiers|length }})</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Entreprise</th>
                            <th>Thème</th>
                            <th>Coût HT</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dossier in dossiers %}
                        <tr>
                            <td>{{ dossier.id }}</td>
                            <td>{{ dossier.fiche_inscription.raison_sociale if dossier.fiche_inscription else 'N/A' }}</td>
                            <td>{{ dossier.theme.nom if dossier.theme else 'N/A' }}</td>
                            <td>{{ dossier.cout_formation_ht }}</td>
                            <td>
                                <a href="{{ url_for('edit_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if remboursements %}
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-file-invoice-dollar"></i> Dossiers de remboursement ({{ remboursements|length }})</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Entreprise</th>
                            <th>Organisme</th>
                            <th>Thème</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for remboursement in remboursements %}
                        <tr>
                            <td>{{ remboursement.id }}</td>
                            <td>{{ remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else 'N/A' }}</td>
                            <td>{{ remboursement.organisme.raison_sociale if remboursement.organisme else 'N/A' }}</td>
                            <td>{{ remboursement.theme }}</td>
                            <td>
                                <a href="{{ url_for('edit_remboursement', id=remboursement.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if organismes %}
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0"><i class="fas fa-building"></i> Organismes ({{ organismes|length }})</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Raison Sociale</th>
                            <th>Forme Juridique</th>
                            <th>Gérant</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for organisme in organismes %}
                        <tr>
                            <td>{{ organisme.id }}</td>
                            <td>{{ organisme.raison_sociale }}</td>
                            <td>{{ organisme.forme_juridique }}</td>
                            <td>{{ organisme.nom_prenom_gerant }}</td>
                            <td>
                                <a href="{{ url_for('edit_organisme', id=organisme.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if formateurs %}
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0"><i class="fas fa-chalkboard-teacher"></i> Formateurs ({{ formateurs|length }})</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom et Prénom</th>
                            <th>Spécialité</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for formateur in formateurs %}
                        <tr>
                            <td>{{ formateur.id }}</td>
                            <td>{{ formateur.nom_prenom }}</td>
                            <td>{{ formateur.specialite }}</td>
                            <td>{{ formateur.email }}</td>
                            <td>
                                <a href="{{ url_for('edit_formateur', id=formateur.id) }}" class="btn btn-sm btn-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
