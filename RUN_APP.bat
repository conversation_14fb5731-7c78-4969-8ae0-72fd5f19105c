@echo off
chcp 65001 >nul
title نظام إدارة التكوين

cls
echo.
echo ============================================================
echo                🌐 نظام إدارة التكوين
echo ============================================================
echo.

REM استخدام الملف التنفيذي إذا كان متوفراً
if exist "dist\GestionFormation.exe" (
    echo ✅ تشغيل الملف التنفيذي...
    echo.
    echo 📋 معلومات الدخول:
    echo    🔗 الرابط: http://127.0.0.1:8080
    echo    👤 المستخدم: admin
    echo    🔑 كلمة المرور: admin123
    echo.
    echo 🚀 البرنامج سيفتح الآن...
    echo ============================================================
    
    start "" "dist\GestionFormation.exe"
    timeout /t 3 /nobreak >nul
    start "" "http://127.0.0.1:8080"
    
    echo ✅ تم تشغيل البرنامج بنجاح!
    goto :end
)

REM إذا لم يكن الملف التنفيذي متوفراً، استخدم Python
echo ⚠️ الملف التنفيذي غير متوفر
echo 🔧 محاولة تشغيل البرنامج بـ Python...
echo.

REM إنشاء قاعدة البيانات إذا لم تكن موجودة
if not exist "formations_central.db" (
    echo 🔧 إنشاء قاعدة البيانات...
    python init_db.py
)

echo 📋 معلومات الدخول:
echo    🔗 الرابط: http://127.0.0.1:8080
echo    👤 المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🚀 تشغيل البرنامج...
echo ============================================================

python run.py

:end
pause
