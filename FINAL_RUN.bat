@echo off
chcp 65001 >nul
title نظام إدارة التكوين

cls
echo.
echo ============================================================
echo                نظام إدارة التكوين
echo ============================================================
echo.

REM إنهاء أي عمليات سابقة
taskkill /f /im python.exe >nul 2>&1

REM التحقق من وجود قاعدة البيانات
if not exist "formations_central.db" (
    echo إنشاء قاعدة البيانات...
    python create_simple_db.py
    if %errorlevel% neq 0 (
        echo فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)

echo.
echo معلومات الدخول:
echo الرابط: http://127.0.0.1:8080
echo المستخدم: admin
echo كلمة المرور: admin123
echo.
echo تشغيل البرنامج...
echo ============================================================

REM تشغيل التطبيق
start /min python run.py

REM انتظار قليل ثم فتح المتصفح
timeout /t 4 /nobreak >nul
start "" "http://127.0.0.1:8080"

echo.
echo ✅ تم تشغيل البرنامج بنجاح!
echo 🌐 تم فتح المتصفح تلقائياً
echo.
echo لإغلاق البرنامج، أغلق هذه النافذة
echo ============================================================

pause
