{% extends "base.html" %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/company.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête de la page -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-building text-primary me-2"></i>
                        Informations de l'Entreprise
                    </h1>
                    <p class="text-muted mb-0">Gestion des informations de l'entreprise pour l'impression et les documents</p>
                </div>
                <div>
                    <a href="{{ url_for('edit_company_info') }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                    <a href="{{ url_for('print_example') }}" class="btn btn-info ms-2" target="_blank">
                        <i class="fas fa-print me-2"></i>Exemple d'impression
                    </a>
                </div>
            </div>

            {% if company %}
            <!-- Informations de l'entreprise -->
            <div class="row">
                <!-- Informations générales -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex justify-content-between align-items-center">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>Informations Générales
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Nom de l'entreprise</label>
                                        <p class="form-control-plaintext">{{ company.nom_entreprise or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Raison sociale</label>
                                        <p class="form-control-plaintext">{{ company.raison_sociale or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Forme juridique</label>
                                        <p class="form-control-plaintext">{{ company.forme_juridique or '-' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Slogan</label>
                                        <p class="form-control-plaintext">{{ company.slogan or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Description</label>
                                        <p class="form-control-plaintext">{{ company.description or '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations de contact -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-address-book me-2"></i>Informations de Contact
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Adresse</label>
                                        <p class="form-control-plaintext">{{ company.get_full_address() or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Téléphone principal</label>
                                        <p class="form-control-plaintext">{{ company.telephone_1 or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Téléphone secondaire</label>
                                        <p class="form-control-plaintext">{{ company.telephone_2 or '-' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Fax</label>
                                        <p class="form-control-plaintext">{{ company.fax or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Email</label>
                                        <p class="form-control-plaintext">{{ company.email or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Site web</label>
                                        <p class="form-control-plaintext">{{ company.site_web or '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations légales -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-gavel me-2"></i>Informations Légales
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Patente</label>
                                        <p class="form-control-plaintext">{{ company.patente or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">Identifiant fiscal</label>
                                        <p class="form-control-plaintext">{{ company.identifiant_fiscal or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">N° RC</label>
                                        <p class="form-control-plaintext">{{ company.num_rc or '-' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">N° CNSS</label>
                                        <p class="form-control-plaintext">{{ company.num_cnss or '-' }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-secondary">ICE</label>
                                        <p class="form-control-plaintext">{{ company.ice or '-' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Panneau latéral -->
                <div class="col-lg-4">
                    <!-- Logo et couleurs -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-palette me-2"></i>Identité Visuelle
                            </h6>
                        </div>
                        <div class="card-body text-center">
                            {% if company.logo_path %}
                            <div class="mb-3">
                                <img src="{{ url_for('static', filename=company.logo_path) }}" 
                                     alt="Logo" class="img-fluid" style="max-height: 150px;">
                            </div>
                            {% else %}
                            <div class="mb-3">
                                <div class="bg-light p-4 rounded">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                    <p class="text-muted mt-2">Aucun logo</p>
                                </div>
                            </div>
                            {% endif %}
                            
                            <div class="row">
                                <div class="col-6">
                                    <label class="form-label fw-bold text-secondary">Couleur principale</label>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div style="width: 40px; height: 40px; background-color: {{ company.couleur_principale or '#007bff' }}; border-radius: 50%; border: 2px solid #dee2e6;"></div>
                                        <small class="ms-2">{{ company.couleur_principale or '#007bff' }}</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <label class="form-label fw-bold text-secondary">Couleur secondaire</label>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <div style="width: 40px; height: 40px; background-color: {{ company.couleur_secondaire or '#6c757d' }}; border-radius: 50%; border: 2px solid #dee2e6;"></div>
                                        <small class="ms-2">{{ company.couleur_secondaire or '#6c757d' }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Statut -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info me-2"></i>Statut
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold text-secondary">État</label>
                                {% if company.actif %}
                                <span class="badge bg-success">Actif</span>
                                {% else %}
                                <span class="badge bg-danger">Inactif</span>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold text-secondary">Dernière modification</label>
                                <p class="form-control-plaintext">
                                    {{ company.date_modification.strftime('%d/%m/%Y à %H:%M') if company.date_modification else '-' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pied de page et mentions légales -->
            {% if company.pied_de_page or company.mentions_legales %}
            <div class="row">
                <div class="col-12">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-file-alt me-2"></i>Informations pour l'Impression
                            </h6>
                        </div>
                        <div class="card-body">
                            {% if company.pied_de_page %}
                            <div class="mb-3">
                                <label class="form-label fw-bold text-secondary">Pied de page</label>
                                <div class="border p-3 bg-light rounded">
                                    {{ company.pied_de_page.replace('\n', '<br>')|safe }}
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if company.mentions_legales %}
                            <div class="mb-3">
                                <label class="form-label fw-bold text-secondary">Mentions légales</label>
                                <div class="border p-3 bg-light rounded">
                                    {{ company.mentions_legales.replace('\n', '<br>')|safe }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            {% else %}
            <!-- Aucune information d'entreprise -->
            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucune information d'entreprise</h4>
                    <p class="text-muted">Configurez les informations de votre entreprise pour les utiliser dans l'impression et les documents.</p>
                    <a href="{{ url_for('edit_company_info') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Ajouter les informations
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
