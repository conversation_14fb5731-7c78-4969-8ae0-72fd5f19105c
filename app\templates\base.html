<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %} - Gestion des Formation</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}"
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if not current_user.is_authenticated and request.endpoint == 'login' %}
        <!-- Show only the main content for login page -->
        <div class="container-fluid">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            {% block login_content %}{% endblock %}
        </div>
    {% else %}
        <!-- Show full layout with sidebar for authenticated users -->
        <div id="wrapper">
            <!-- Sidebar -->
            <div id="sidebar-wrapper" class="sidebar">
                <div class="sidebar-heading">
                    <i class="fas fa-graduation-cap text-primary"></i> GESTION DES FORMATIONS
                </div>
                <div class="nav flex-column">
                    <a href="{{ url_for('dashboard') }}" class="nav-link">
                        <i class="fas fa-tachometer-alt"></i> Tableau de Bord
                    </a>
                    <a href="{{ url_for('fiches_inscription') }}" class="nav-link">
                        <i class="fas fa-file-alt"></i> Fiches d'Inscription
                    </a>
                    <a href="{{ url_for('dossiers_techniques') }}" class="nav-link">
                        <i class="fas fa-folder"></i> Dossiers Techniques
                    </a>
                    <a href="{{ url_for('remboursements') }}" class="nav-link">
                        <i class="fas fa-file-invoice-dollar"></i> Remboursements
                    </a>
                    <a href="{{ url_for('organismes') }}" class="nav-link">
                        <i class="fas fa-building"></i> Organismes
                    </a>
                    <a href="{{ url_for('formateurs') }}" class="nav-link">
                        <i class="fas fa-chalkboard-teacher"></i> Formateurs
                    </a>
                    <a href="{{ url_for('agenda_formateurs') }}" class="nav-link">
                        <i class="fas fa-calendar-alt"></i> Agenda des Formateurs
                    </a>
                    <a href="{{ url_for('domaines_themes') }}" class="nav-link">
                        <i class="fas fa-sitemap"></i> Domaines et Thèmes
                    </a>
                    {% if current_user.is_admin %}
                    <a href="{{ url_for('users') }}" class="nav-link">
                        <i class="fas fa-user-cog"></i> Utilisateurs
                    </a>
                    <hr class="sidebar-divider">
                    <div class="sidebar-heading text-muted small">
                        ADMINISTRATION
                    </div>
                    <a href="{{ url_for('sauvegardes') }}" class="nav-link">
                        <i class="fas fa-database"></i> Sauvegardes
                    </a>
                    <a href="{{ url_for('journal_activite') }}" class="nav-link">
                        <i class="fas fa-history"></i> Journal d'Activité
                    </a>
                    <a href="{{ url_for('company_info') }}" class="nav-link">
                        <i class="fas fa-building"></i> Informations Entreprise
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Page Content -->
            <div id="page-content-wrapper">
                <!-- Navbar -->
                <nav class="navbar navbar-expand-lg">
                    <div class="container-fluid">
                        <button class="btn btn-link" id="menu-toggle">
                            <i class="fas fa-bars text-primary"></i>
                        </button>
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <!-- Espace pour le titre de la page -->
                            <span class="navbar-text mx-auto">
                                {{ title if title else 'Gestion des Formation' }}
                            </span>

                            <ul class="navbar-nav ms-auto">
                                {% if current_user.is_authenticated %}
                                    <li class="nav-item dropdown">
                                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-user"></i> {{ current_user.username }}
                                        </a>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-user-cog"></i> Profil
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                                <i class="fas fa-sign-out-alt"></i> Déconnexion
                                            </a></li>
                                        </ul>
                                    </li>
                                {% else %}
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{ url_for('login') }}">
                                            <i class="fas fa-sign-in-alt"></i> Connexion
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </nav>

                <!-- Main Content -->
                <div class="main-content">
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    {% endif %}

    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 pour les notifications -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/darkmode.js') }}"></script>
    <script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>

    <script>
        document.getElementById("menu-toggle")?.addEventListener("click", function(e) {
            e.preventDefault();
            document.getElementById("wrapper").classList.toggle("toggled");
        });

        // Initialize all tooltips
        $(function () {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });

        // Initialize all popovers
        $(function () {
            $('[data-bs-toggle="popover"]').popover();
        });

        // Afficher les messages flash avec SweetAlert2
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    document.addEventListener('DOMContentLoaded', function() {
                        Swal.fire({
                            title: '{{ "Succès" if category == "success" else "Erreur" if category == "danger" else "Information" }}',
                            text: '{{ message }}',
                            icon: '{{ "success" if category == "success" else "error" if category == "danger" else "info" }}',
                            confirmButtonColor: '#4e73df'
                        });
                    });
                {% endfor %}
            {% endif %}
        {% endwith %}
    </script>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/darkmode.js') }}"></script>
    <script src="{{ url_for('static', filename='js/form-validation.js') }}"></script>
    {% block extra_js %}{% endblock %}

    <!-- Bouton de basculement du mode sombre -->
    <div class="dark-mode-toggle" id="darkModeToggle">
        <i class="fas fa-moon"></i>
    </div>
</body>
</html>
