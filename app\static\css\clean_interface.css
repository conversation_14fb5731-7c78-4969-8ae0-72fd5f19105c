/*
 * Interface Professionnelle Claire - Gestion des Formation
 * ABOULFADEL - 2025 - Design professionnel sans arrière-plans sombres
 */

/* Variables CSS pour une interface professionnelle claire */
:root {
    --primary-color: #0056b3;
    --secondary-color: #f8f9fa;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #ffffff;
    --professional-blue: #0056b3;
    --white: #ffffff;
    --border-color: #dee2e6;
    --text-color: #495057;
    --text-muted: #6c757d;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Reset et base */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* Suppression des arrière-plans sombres */
.bg-dark, .navbar-dark, .bg-black {
    background-color: var(--white) !important;
    color: var(--text-color) !important;
}

/* Navigation propre */
.navbar {
    background-color: var(--white) !important;
    border-bottom: 2px solid var(--primary-color);
    box-shadow: var(--shadow);
    padding: 1rem 0;
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    color: var(--text-color) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color) !important;
}

/* Sidebar propre */
.sidebar {
    background-color: var(--white) !important;
    border-right: 1px solid var(--border-color);
    min-height: calc(100vh - 76px);
    padding: 1.5rem 0;
}

.sidebar .nav-link {
    color: var(--text-color) !important;
    padding: 0.75rem 1.5rem;
    border-radius: 0;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--secondary-color);
    border-left-color: var(--primary-color);
    color: var(--primary-color) !important;
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
}

/* Contenu principal */
.main-content {
    background-color: var(--white);
    min-height: calc(100vh - 76px);
    padding: 2rem;
}

/* Cards propres */
.card {
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    box-shadow: var(--shadow);
    background-color: var(--white);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.card-body {
    padding: 1.5rem;
}

/* Boutons propres */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: #1e3d72;
    border-color: #1e3d72;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-light {
    background-color: var(--light-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

/* Formulaires propres */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
    outline: 0;
}

.form-label {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

/* Tables propres */
.table {
    background-color: var(--white);
    border-collapse: separate;
    border-spacing: 0;
}

.table th {
    background-color: var(--secondary-color);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-color);
    padding: 1rem;
}

.table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 249, 250, 0.5);
}

.table-hover tbody tr:hover {
    background-color: var(--secondary-color);
}

/* Alerts propres */
.alert {
    border: 1px solid transparent;
    border-radius: 0.5rem;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Modals propres */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-title {
    color: var(--text-color);
    font-weight: 600;
}

/* Pagination propre */
.pagination .page-link {
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 0.75rem;
}

.pagination .page-link:hover {
    background-color: var(--secondary-color);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Badges propres */
.badge {
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

/* Responsive */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
    }
}

/* Animations douces */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Suppression des éléments sombres spécifiques */
.bg-secondary {
    background-color: var(--light-color) !important;
}

.text-white {
    color: var(--text-color) !important;
}

/* Footer propre */
.footer {
    background-color: var(--white);
    border-top: 1px solid var(--border-color);
    padding: 1rem 0;
    margin-top: 2rem;
    color: var(--text-muted);
    text-align: center;
}

/* Icônes */
.icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

/* Amélioration de la lisibilité */
.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

/* Espacement cohérent */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

.p-4 {
    padding: 1.5rem !important;
}

/* Styles pour le logo */
.navbar-brand img {
    height: 30px;
    width: auto;
    margin-right: 10px;
    border-radius: 4px;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
}

.logo-container img {
    max-height: 80px;
    width: auto;
}

.login-logo {
    max-height: 120px;
    width: auto;
    margin-bottom: 20px;
}
