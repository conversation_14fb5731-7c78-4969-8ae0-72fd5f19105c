{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.total_activites }}</h4>
                            <p class="mb-0">Total Activités</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.activites_aujourd_hui }}</h4>
                            <p class="mb-0">Aujourd'hui</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.top_utilisateurs|length }}</h4>
                            <p class="mb-0">Utilisateurs Actifs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ stats.top_operations|length }}</h4>
                            <p class="mb-0">Types d'Opérations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cogs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-history me-2"></i>
                        Journal d'Activité
                    </h3>
                    <div>
                        <a href="{{ url_for('exporter_journal') }}" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>
                            Exporter CSV
                        </a>
                        <button type="button" class="btn btn-danger" onclick="confirmerVidage()">
                            <i class="fas fa-trash me-1"></i>
                            Vider le Journal
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Formulaire de filtrage -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form method="POST" class="row g-3">
                                {{ form.hidden_tag() }}
                                <div class="col-md-3">
                                    {{ form.nom_utilisateur.label(class="form-label") }}
                                    {{ form.nom_utilisateur(class="form-control", placeholder="Nom d'utilisateur...") }}
                                </div>
                                <div class="col-md-2">
                                    {{ form.type_operation.label(class="form-label") }}
                                    {{ form.type_operation(class="form-select") }}
                                </div>
                                <div class="col-md-2">
                                    {{ form.date_debut.label(class="form-label") }}
                                    {{ form.date_debut(class="form-control") }}
                                </div>
                                <div class="col-md-2">
                                    {{ form.date_fin.label(class="form-label") }}
                                    {{ form.date_fin(class="form-control") }}
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    {{ form.submit(class="btn btn-primary me-2") }}
                                    <a href="{{ url_for('journal_activite') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i>
                                        Effacer
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>

                    {% if activites.items %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Utilisateur</th>
                                    <th>Opération</th>
                                    <th>Description</th>
                                    <th>Date & Heure</th>
                                    <th>Adresse IP</th>
                                    <th>Navigateur</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activite in activites.items %}
                                <tr>
                                    <td>{{ activite.id }}</td>
                                    <td>
                                        <i class="fas fa-user me-1"></i>
                                        {{ activite.nom_utilisateur }}
                                    </td>
                                    <td>
                                        {% if activite.type_operation == 'connexion' %}
                                            <span class="badge bg-success">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'deconnexion' %}
                                            <span class="badge bg-secondary">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'creation' %}
                                            <span class="badge bg-primary">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'modification' %}
                                            <span class="badge bg-warning">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'suppression' %}
                                            <span class="badge bg-danger">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'consultation' %}
                                            <span class="badge bg-info">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'export' %}
                                            <span class="badge bg-dark">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'sauvegarde' %}
                                            <span class="badge bg-success">{{ activite.type_operation }}</span>
                                        {% elif activite.type_operation == 'restauration' %}
                                            <span class="badge bg-warning">{{ activite.type_operation }}</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">{{ activite.type_operation }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span title="{{ activite.description }}">
                                            {{ activite.description[:80] + '...' if activite.description and activite.description|length > 80 else activite.description or 'Aucune description' }}
                                        </span>
                                    </td>
                                    <td>
                                        <i class="fas fa-clock me-1"></i>
                                        {{ activite.date_operation.strftime('%d/%m/%Y %H:%M:%S') }}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ activite.adresse_ip or 'N/A' }}</span>
                                    </td>
                                    <td>
                                        <span class="text-muted" title="{{ activite.user_agent }}">
                                            {% if activite.user_agent %}
                                                {% if 'Chrome' in activite.user_agent %}
                                                    <i class="fab fa-chrome"></i> Chrome
                                                {% elif 'Firefox' in activite.user_agent %}
                                                    <i class="fab fa-firefox"></i> Firefox
                                                {% elif 'Safari' in activite.user_agent %}
                                                    <i class="fab fa-safari"></i> Safari
                                                {% elif 'Edge' in activite.user_agent %}
                                                    <i class="fab fa-edge"></i> Edge
                                                {% else %}
                                                    <i class="fas fa-globe"></i> Autre
                                                {% endif %}
                                            {% else %}
                                                N/A
                                            {% endif %}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if activites.pages > 1 %}
                    <nav aria-label="Navigation du journal">
                        <ul class="pagination justify-content-center">
                            {% if activites.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('journal_activite', page=activites.prev_num, **request.args) }}">Précédent</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in activites.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != activites.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('journal_activite', page=page_num, **request.args) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if activites.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('journal_activite', page=activites.next_num, **request.args) }}">Suivant</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune activité trouvée</h5>
                        <p class="text-muted">Le journal d'activité est vide ou aucune activité ne correspond aux critères de filtrage.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation de vidage -->
<div class="modal fade" id="confirmVidageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer le Vidage</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir vider complètement le journal d'activité ?</p>
                <p class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>Cette action supprimera définitivement toutes les entrées du journal.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <a href="{{ url_for('vider_journal') }}" class="btn btn-danger">Vider le Journal</a>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerVidage() {
    new bootstrap.Modal(document.getElementById('confirmVidageModal')).show();
}
</script>
{% endblock %}
