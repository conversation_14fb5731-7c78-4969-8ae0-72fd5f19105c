{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.fiche_inscription.label(class="form-control-label") }}
                                    {{ form.fiche_inscription(class="form-control") }}
                                    {% for error in form.fiche_inscription.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.domaine.label(class="form-control-label") }}
                                    {{ form.domaine(class="form-control") }}
                                    {% for error in form.domaine.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.theme.label(class="form-control-label") }}
                                    {{ form.theme(class="form-control") }}
                                    {% for error in form.theme.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.objectif.label(class="form-control-label") }}
                                    {{ form.objectif(class="form-control", rows=4) }}
                                    {% for error in form.objectif.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.contenu_indicatif.label(class="form-control-label") }}
                                    {{ form.contenu_indicatif(class="form-control", rows=4) }}
                                    {% for error in form.contenu_indicatif.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.organisme_formation.label(class="form-control-label") }}
                                    {{ form.organisme_formation(class="form-control") }}
                                    {% for error in form.organisme_formation.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.num_cnss_organisme.label(class="form-control-label") }}
                                    {{ form.num_cnss_organisme(class="form-control") }}
                                    {% for error in form.num_cnss_organisme.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.type_formation.label(class="form-control-label") }}
                                    {{ form.type_formation(class="form-control") }}
                                    {% for error in form.type_formation.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.cout_formation_ht.label(class="form-control-label") }}
                                    {{ form.cout_formation_ht(class="form-control") }}
                                    {% for error in form.cout_formation_ht.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.effectif_global.label(class="form-control-label") }}
                                    {{ form.effectif_global(class="form-control") }}
                                    {% for error in form.effectif_global.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.nombre_cadres.label(class="form-control-label") }}
                                    {{ form.nombre_cadres(class="form-control") }}
                                    {% for error in form.nombre_cadres.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.nombre_employes.label(class="form-control-label") }}
                                    {{ form.nombre_employes(class="form-control") }}
                                    {% for error in form.nombre_employes.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.nombre_ouvriers.label(class="form-control-label") }}
                                    {{ form.nombre_ouvriers(class="form-control") }}
                                    {% for error in form.nombre_ouvriers.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.conforme(class="form-check-input") }}
                                        {{ form.conforme.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.depot_physique(class="form-check-input") }}
                                        {{ form.depot_physique.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    {{ form.date_depot.label(class="form-control-label") }}
                                    {{ form.date_depot(class="form-control") }}
                                    {% for error in form.date_depot.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.validation(class="form-check-input") }}
                                        {{ form.validation.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    {{ form.piece_jointe.label(class="form-control-label") }}
                                    {{ form.piece_jointe(class="form-control-file") }}
                                    {% for error in form.piece_jointe.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                {% if dossier.piece_jointe %}
                                <div class="form-group">
                                    <p>Pièce jointe actuelle: {{ dossier.piece_jointe }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('dossiers_techniques') }}" class="btn btn-secondary">Annuler</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
