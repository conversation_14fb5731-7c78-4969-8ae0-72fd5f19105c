{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-folder"></i> Dossiers Techniques</h1>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <a href="{{ url_for('new_dossier_technique') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouveau Dossier
            </a>
        </div>
        <div class="col-md-4">
            <form action="{{ url_for('search_dossiers') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un dossier..." value="{{ query }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Liste des Dossiers Techniques</h5>
                </div>
                <div class="card-body">
                    {% if dossiers %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Entreprise</th>
                                    <th>Domaine</th>
                                    <th>Thème</th>
                                    <th>Coût HT</th>
                                    <th>Conforme</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dossier in dossiers %}
                                <tr>
                                    <td>{{ dossier.id }}</td>
                                    <td>{{ dossier.fiche_inscription.raison_sociale if dossier.fiche_inscription else 'N/A' }}</td>
                                    <td>{{ dossier.domaine.nom if dossier.domaine else 'N/A' }}</td>
                                    <td>{{ dossier.theme.nom if dossier.theme else 'N/A' }}</td>
                                    <td>{{ dossier.cout_formation_ht }}</td>
                                    <td>
                                        {% if dossier.conforme %}
                                        <span class="badge bg-success">Oui</span>
                                        {% else %}
                                        <span class="badge bg-danger">Non</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{{ url_for('delete_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce dossier?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        Aucun dossier technique n'a été trouvé.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
