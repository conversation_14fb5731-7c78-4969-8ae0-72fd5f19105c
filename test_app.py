#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار لتشخيص مشاكل التطبيق
"""

import sys
import traceback

def test_imports():
    """اختبار الاستيرادات"""
    print("🔍 اختبار الاستيرادات...")
    
    try:
        from flask import Flask
        print("✅ Flask - OK")
    except Exception as e:
        print(f"❌ Flask - خطأ: {e}")
        return False
    
    try:
        from app import create_app
        print("✅ create_app - OK")
    except Exception as e:
        print(f"❌ create_app - خطأ: {e}")
        traceback.print_exc()
        return False
    
    try:
        from app.models import User
        print("✅ Models - OK")
    except Exception as e:
        print(f"❌ Models - خطأ: {e}")
        traceback.print_exc()
        return False
    
    try:
        from app.routes import login
        print("✅ Routes - OK")
    except Exception as e:
        print(f"❌ Routes - خطأ: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_app_creation():
    """اختبار إنشاء التطبيق"""
    print("\n🔍 اختبار إنشاء التطبيق...")
    
    try:
        from app import create_app
        app = create_app()
        print("✅ إنشاء التطبيق - OK")
        return app
    except Exception as e:
        print(f"❌ إنشاء التطبيق - خطأ: {e}")
        traceback.print_exc()
        return None

def test_routes():
    """اختبار المسارات"""
    print("\n🔍 اختبار المسارات...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            # اختبار الصفحة الرئيسية
            response = client.get('/')
            print(f"✅ الصفحة الرئيسية - كود الاستجابة: {response.status_code}")
            
            if response.status_code == 500:
                print("❌ خطأ 500 في الصفحة الرئيسية")
                return False
            
            # اختبار صفحة تسجيل الدخول
            response = client.get('/login')
            print(f"✅ صفحة تسجيل الدخول - كود الاستجابة: {response.status_code}")
            
            if response.status_code == 500:
                print("❌ خطأ 500 في صفحة تسجيل الدخول")
                return False
                
        return True
    except Exception as e:
        print(f"❌ اختبار المسارات - خطأ: {e}")
        traceback.print_exc()
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🔍 اختبار قاعدة البيانات...")
    
    try:
        from app import create_app, db
        from app.models import User
        
        app = create_app()
        with app.app_context():
            # اختبار الاتصال بقاعدة البيانات
            users = User.query.all()
            print(f"✅ قاعدة البيانات - عدد المستخدمين: {len(users)}")
            
            # اختبار المستخدم الافتراضي
            admin_user = User.query.filter_by(username='admin').first()
            if admin_user:
                print("✅ المستخدم الافتراضي موجود")
            else:
                print("⚠️ المستخدم الافتراضي غير موجود")
                
        return True
    except Exception as e:
        print(f"❌ اختبار قاعدة البيانات - خطأ: {e}")
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🔧 تشخيص مشاكل نظام إدارة التكوين")
    print("=" * 60)
    
    # اختبار الاستيرادات
    if not test_imports():
        print("\n❌ فشل في اختبار الاستيرادات")
        return
    
    # اختبار إنشاء التطبيق
    app = test_app_creation()
    if not app:
        print("\n❌ فشل في إنشاء التطبيق")
        return
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل في اختبار قاعدة البيانات")
        return
    
    # اختبار المسارات
    if not test_routes():
        print("\n❌ فشل في اختبار المسارات")
        return
    
    print("\n" + "=" * 60)
    print("✅ جميع الاختبارات نجحت!")
    print("🚀 يمكن تشغيل التطبيق الآن")
    print("=" * 60)

if __name__ == '__main__':
    main()
