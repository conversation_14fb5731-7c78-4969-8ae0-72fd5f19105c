{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-file-alt"></i> Nouvelle Fiche d'Inscription</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations de l'Entreprise</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.raison_sociale.label(class="form-control-label") }}
                            {{ form.raison_sociale(class="form-control") }}
                            {% if form.raison_sociale.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.raison_sociale.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.tel_entreprise.label(class="form-control-label") }}
                            {{ form.tel_entreprise(class="form-control") }}
                            {% if form.tel_entreprise.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.tel_entreprise.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.fax_entreprise.label(class="form-control-label") }}
                            {{ form.fax_entreprise(class="form-control") }}
                            {% if form.fax_entreprise.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.fax_entreprise.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.mot_de_passe_email.label(class="form-control-label") }}
                            {{ form.mot_de_passe_email(class="form-control") }}
                            {% if form.mot_de_passe_email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.mot_de_passe_email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.patente.label(class="form-control-label") }}
                            {{ form.patente(class="form-control") }}
                            {% if form.patente.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.patente.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.identifiant_fiscale.label(class="form-control-label") }}
                            {{ form.identifiant_fiscale(class="form-control") }}
                            {% if form.identifiant_fiscale.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.identifiant_fiscale.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.num_rc.label(class="form-control-label") }}
                            {{ form.num_rc(class="form-control") }}
                            {% if form.num_rc.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.num_rc.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.num_cnss.label(class="form-control-label") }}
                            {{ form.num_cnss(class="form-control") }}
                            {% if form.num_cnss.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.num_cnss.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.ice.label(class="form-control-label") }}
                            {{ form.ice(class="form-control") }}
                            {% if form.ice.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ice.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.mot_de_passe_ice.label(class="form-control-label") }}
                            {{ form.mot_de_passe_ice(class="form-control") }}
                            {% if form.mot_de_passe_ice.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.mot_de_passe_ice.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.eligible(class="form-check-input") }}
                            {{ form.eligible.label(class="form-check-label") }}
                            {% if form.eligible.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.eligible.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.nombre_cadres.label(class="form-control-label") }}
                            {{ form.nombre_cadres(class="form-control") }}
                            {% if form.nombre_cadres.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nombre_cadres.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.nombre_employes.label(class="form-control-label") }}
                            {{ form.nombre_employes(class="form-control") }}
                            {% if form.nombre_employes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nombre_employes.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.nombre_ouvriers.label(class="form-control-label") }}
                            {{ form.nombre_ouvriers(class="form-control") }}
                            {% if form.nombre_ouvriers.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nombre_ouvriers.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.validation(class="form-check-input") }}
                            {{ form.validation.label(class="form-check-label") }}
                            {% if form.validation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.validation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date_validation.label(class="form-control-label") }}
                            {{ form.date_validation(class="form-control", type="date") }}
                            {% if form.date_validation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date_validation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.depot_physique(class="form-check-input") }}
                            {{ form.depot_physique.label(class="form-check-label") }}
                            {% if form.depot_physique.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.depot_physique.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date_depot.label(class="form-control-label") }}
                            {{ form.date_depot(class="form-control", type="date") }}
                            {% if form.date_depot.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date_depot.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.piece_jointe.label(class="form-control-label") }}
                    {{ form.piece_jointe(class="form-control-file") }}
                    {% if form.piece_jointe.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.piece_jointe.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-text text-muted">Formats acceptés: PDF, PNG, JPG, JPEG</small>
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('fiches_inscription') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
