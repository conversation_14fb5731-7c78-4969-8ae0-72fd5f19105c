{% extends "print_layout.html" %}

{% block content %}
<div class="info-section">
    <h2>Informations Générales</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Raison Sociale:</span>
                <span>{{ organisme.raison_sociale }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">ICE:</span>
                <span>{{ organisme.ice }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Numéro CNSS:</span>
                <span>{{ organisme.num_cnss }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Patente:</span>
                <span>{{ organisme.patente }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Identifiant Fiscal:</span>
                <span>{{ organisme.identifiant_fiscal }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">N° RC:</span>
                <span>{{ organisme.num_rc }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Forme Juridique:</span>
                <span>{{ organisme.forme_juridique }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Adresse:</span>
                <span>{{ organisme.adresse }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Ville:</span>
                <span>{{ organisme.ville }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Téléphone:</span>
                <span>{{ organisme.telephone }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span>{{ organisme.email }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Fax:</span>
                <span>{{ organisme.fax }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations du Contact</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Nom et Prénom:</span>
                <span>{{ organisme.nom_contact }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Fonction:</span>
                <span>{{ organisme.fonction_contact }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Téléphone:</span>
                <span>{{ organisme.tel_contact }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span>{{ organisme.email_contact }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Bancaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Banque:</span>
                <span>{{ organisme.banque }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Agence:</span>
                <span>{{ organisme.agence }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">RIB:</span>
                <span>{{ organisme.rib }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Swift:</span>
                <span>{{ organisme.swift }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Complémentaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Date de Création:</span>
                <span>{{ organisme.date_creation.strftime('%d/%m/%Y') if organisme.date_creation else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Actif:</span>
                <span>{{ 'Oui' if organisme.actif else 'Non' }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Créé par:</span>
                <span>{{ organisme.user.nom_complet if organisme.user else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Notes:</span>
                <span>{{ organisme.notes }}</span>
            </div>
        </div>
    </div>
</div>

{% if organisme.dossiers_techniques %}
<div class="info-section">
    <h2>Dossiers Techniques Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Entreprise</th>
                <th>Thème</th>
                <th>Date de Début</th>
                <th>Coût HT</th>
            </tr>
        </thead>
        <tbody>
            {% for dossier in organisme.dossiers_techniques %}
            <tr>
                <td>{{ dossier.id }}</td>
                <td>{{ dossier.fiche_inscription.raison_sociale if dossier.fiche_inscription else 'N/A' }}</td>
                <td>{{ dossier.theme.nom if dossier.theme else 'N/A' }}</td>
                <td>{{ dossier.date_debut.strftime('%d/%m/%Y') if dossier.date_debut else 'N/A' }}</td>
                <td>{{ dossier.cout_formation_ht }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

{% if organisme.dossiers_remboursement %}
<div class="info-section">
    <h2>Dossiers de Remboursement Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Entreprise</th>
                <th>Thème</th>
                <th>Date</th>
                <th>Montant</th>
            </tr>
        </thead>
        <tbody>
            {% for remboursement in organisme.dossiers_remboursement %}
            <tr>
                <td>{{ remboursement.id }}</td>
                <td>{{ remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else 'N/A' }}</td>
                <td>{{ remboursement.theme }}</td>
                <td>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else 'N/A' }}</td>
                <td>{{ remboursement.montant }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}
{% endblock %}
