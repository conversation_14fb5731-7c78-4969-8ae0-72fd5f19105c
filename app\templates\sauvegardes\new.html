{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus me-2"></i>
                        Nouvelle Sauvegarde
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.type_sauvegarde.label(class="form-label") }}
                                    {{ form.type_sauvegarde(class="form-select") }}
                                    {% if form.type_sauvegarde.errors %}
                                        <div class="text-danger">
                                            {% for error in form.type_sauvegarde.errors %}
                                                <small>{{ error }}</small>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows="4", placeholder="Description optionnelle de la sauvegarde...") }}
                            {% if form.description.errors %}
                                <div class="text-danger">
                                    {% for error in form.description.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Ajoutez une description pour identifier facilement cette sauvegarde.
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information :</strong> La sauvegarde créera une copie complète de la base de données actuelle. 
                            Le processus peut prendre quelques secondes selon la taille de vos données.
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('sauvegardes') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>
                                Retour
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Informations sur les sauvegardes -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        À propos des Sauvegardes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-hand-paper me-1 text-primary"></i> Sauvegarde Manuelle</h6>
                            <p class="text-muted small">
                                Créée à la demande par un administrateur. Idéale avant des modifications importantes 
                                ou des mises à jour du système.
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-robot me-1 text-info"></i> Sauvegarde Automatique</h6>
                            <p class="text-muted small">
                                Créée automatiquement par le système selon une planification définie. 
                                Assure une protection continue des données.
                            </p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-12">
                            <h6><i class="fas fa-shield-alt me-1 text-success"></i> Bonnes Pratiques</h6>
                            <ul class="text-muted small">
                                <li>Créez une sauvegarde avant toute modification importante</li>
                                <li>Vérifiez régulièrement l'intégrité de vos sauvegardes</li>
                                <li>Conservez plusieurs versions de sauvegardes</li>
                                <li>Stockez les sauvegardes importantes dans un lieu sûr</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
