{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-file-alt"></i> Fiches d'Inscription</h1>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-group" role="group">
                <a href="{{ url_for('new_fiche_inscription') }}" class="btn btn-primary">
                    <i class="fas fa-plus-circle"></i> Nouvelle Fiche
                </a>
                <a href="{{ url_for('eligibilite_ofppt') }}" class="btn btn-info">
                    <i class="fas fa-check-circle"></i> Éligibilité OFPPT
                </a>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_fiches') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher une fiche..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Fiches d'Inscription</h5>
        </div>
        <div class="card-body">
            {% if fiches %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Raison Sociale</th>
                                <th>Date d'Inscription</th>
                                <th>Téléphone</th>
                                <th>Email</th>
                                <th>Éligible</th>
                                <th>Validation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for fiche in fiches %}
                                <tr>
                                    <td>{{ fiche.id }}</td>
                                    <td>{{ fiche.raison_sociale }}</td>
                                    <td>{{ fiche.date_inscription.strftime('%d/%m/%Y') }}</td>
                                    <td>{{ fiche.tel_entreprise }}</td>
                                    <td>{{ fiche.email }}</td>
                                    <td>
                                        {% if fiche.eligible %}
                                            <span class="badge badge-success">Oui</span>
                                        {% else %}
                                            <span class="badge badge-danger">Non</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if fiche.validation %}
                                            <span class="badge badge-success">Validé</span>
                                        {% else %}
                                            <span class="badge badge-warning">En attente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_fiche_inscription', id=fiche.id) }}" class="btn btn-sm btn-info" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('delete_fiche_inscription', id=fiche.id) }}" class="btn btn-sm btn-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette fiche?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    Aucune fiche d'inscription trouvée. <a href="{{ url_for('new_fiche_inscription') }}">Créer une nouvelle fiche</a>.
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='fiches_inscription') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
