{% extends "base.html" %}

{% block title %}Connexion{% endblock %}

{% block login_content %}
<div class="login-container">
    <div class="login-card animate__animated animate__fadeIn">
        <div class="login-form-container">
            <h2 class="login-title animate__animated animate__fadeInDown">Bienvenue</h2>
            <p class="login-subtitle animate__animated animate__fadeInDown animate__delay-1s">
                Connectez-vous pour accéder à votre espace de gestion des formations
            </p>

            <form method="POST" action="" id="loginForm" class="mt-4">
                {{ form.hidden_tag() }}

                <div class="mb-4 animate-form-element">
                    <div class="form-floating">
                        {{ form.username(class="form-control", id="username", placeholder="Nom d'utilisateur") }}
                        {{ form.username.label(for="username") }}
                    </div>
                    {% for error in form.username.errors %}
                    <div class="text-danger small mt-1">{{ error }}</div>
                    {% endfor %}
                </div>

                <div class="mb-4 animate-form-element">
                    <div class="form-floating">
                        {{ form.password(class="form-control", id="password", placeholder="Mot de passe") }}
                        {{ form.password.label(for="password") }}
                    </div>
                    {% for error in form.password.errors %}
                    <div class="text-danger small mt-1">{{ error }}</div>
                    {% endfor %}
                </div>

                <div class="mb-4 form-check animate-form-element">
                    {{ form.remember_me(class="form-check-input", id="remember_me") }}
                    {{ form.remember_me.label(class="form-check-label", for="remember_me") }}
                </div>

                <div class="d-grid gap-2 animate-form-element">
                    {{ form.submit(class="btn btn-primary btn-lg") }}
                </div>
            </form>

            <div class="mt-4 text-center animate__animated animate__fadeInUp animate__delay-1s">
                <p class="text-muted">
                    Système de gestion des formations professionnelles
                </p>
                <p class="text-muted small">
                    Développé pour améliorer la gestion de vos formations
                </p>
            </div>
        </div>

        <div class="login-image-container">
            <img src="{{ url_for('static', filename='images/Download premium image of Men white background accessories electronics_  about cartoon, person, men, shirt, and 3d        12112385 (1).png') }}"
                 alt="Gestionnaire de formation"
                 class="img-fluid animate__animated animate__fadeIn animate__delay-1s">
            <div class="login-description">
                <h3>Plateforme de Gestion</h3>
                <p>Une solution complète pour gérer efficacement vos formations professionnelles, formateurs, et organismes de formation. Simplifiez votre gestion administrative et suivez vos dossiers en temps réel.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
