@echo off
chcp 65001 >nul
title نظام إدارة التكوين

cls
echo.
echo ============================================================
echo                🌐 نظام إدارة التكوين
echo ============================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير مثبت أو غير مُعرف في PATH
    echo 💡 يرجى تثبيت Python 3.11 من python.org
    pause
    exit /b 1
)

REM التحقق من وجود البيئة الافتراضية
if not exist "venv" (
    echo 🔧 إنشاء البيئة الافتراضية...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
)

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ فشل في تفعيل البيئة الافتراضية
    pause
    exit /b 1
)

REM تثبيت المتطلبات
echo 🔧 تثبيت المتطلبات...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

REM إنشاء قاعدة البيانات
if not exist "formations_central.db" (
    echo 🔧 إنشاء قاعدة البيانات...
    python init_db.py
    if %errorlevel% neq 0 (
        echo ❌ فشل في إنشاء قاعدة البيانات
        pause
        exit /b 1
    )
)

echo.
echo ✅ البرنامج جاهز للتشغيل!
echo.
echo 📋 معلومات الدخول:
echo    🔗 الرابط: http://127.0.0.1:8080
echo    👤 المستخدم: admin
echo    🔑 كلمة المرور: admin123
echo.
echo 🚀 تشغيل البرنامج...
echo ============================================================

REM تشغيل التطبيق
python run.py

pause
