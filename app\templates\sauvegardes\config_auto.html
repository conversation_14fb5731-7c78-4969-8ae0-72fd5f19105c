{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-clock"></i> Configuration des Sauvegardes Automatiques</h2>
                <a href="{{ url_for('sauvegardes') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Retour aux Sauvegardes
                </a>
            </div>

            <!-- Statut actuel -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> Statut du Planificateur
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if statut.actif %}
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> 
                                    <strong>Actif</strong> - Le planificateur est en cours d'exécution
                                </div>
                                {% if statut.prochaine_execution %}
                                <p><strong>Prochaine sauvegarde :</strong> {{ statut.prochaine_execution.strftime('%d/%m/%Y à %H:%M') }}</p>
                                {% endif %}
                                <p><strong>Tâches programmées :</strong> {{ statut.nombre_jobs }}</p>
                            {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> 
                                    <strong>Inactif</strong> - Aucune sauvegarde automatique programmée
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cog"></i> Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if not statut.actif %}
                                <!-- Formulaire pour démarrer -->
                                <form method="POST" class="mb-3">
                                    <input type="hidden" name="action" value="demarrer">
                                    <div class="mb-3">
                                        <label for="heure" class="form-label">Heure de sauvegarde quotidienne :</label>
                                        <input type="time" class="form-control" id="heure" name="heure" value="02:00" required>
                                        <div class="form-text">Format 24h (ex: 02:00 pour 2h du matin)</div>
                                    </div>
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-play"></i> Démarrer les Sauvegardes Automatiques
                                    </button>
                                </form>
                            {% else %}
                                <!-- Bouton pour arrêter -->
                                <form method="POST" onsubmit="return confirm('Êtes-vous sûr de vouloir arrêter les sauvegardes automatiques ?')">
                                    <input type="hidden" name="action" value="arreter">
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-stop"></i> Arrêter les Sauvegardes Automatiques
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Informations sur les sauvegardes automatiques -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info"></i> À propos des Sauvegardes Automatiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-check text-success"></i> Avantages :</h6>
                            <ul>
                                <li>Sauvegarde quotidienne automatique de votre base de données</li>
                                <li>Protection contre la perte de données</li>
                                <li>Aucune intervention manuelle requise</li>
                                <li>Nettoyage automatique des anciennes sauvegardes</li>
                                <li>Enregistrement dans le journal d'activité</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-cog text-info"></i> Fonctionnement :</h6>
                            <ul>
                                <li>Exécution quotidienne à l'heure configurée</li>
                                <li>Conservation des 7 dernières sauvegardes automatiques</li>
                                <li>Suppression automatique des sauvegardes plus anciennes</li>
                                <li>Fonctionnement en arrière-plan</li>
                                <li>Redémarrage automatique avec l'application</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-lightbulb"></i>
                        <strong>Conseil :</strong> Il est recommandé de programmer les sauvegardes automatiques 
                        pendant les heures creuses (par exemple 2h du matin) pour éviter d'impacter les performances 
                        pendant les heures d'utilisation.
                    </div>
                </div>
            </div>

            <!-- Historique des sauvegardes automatiques récentes -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history"></i> Dernières Sauvegardes Automatiques
                    </h5>
                </div>
                <div class="card-body">
                    {% set sauvegardes_auto = [] %}
                    {% for sauvegarde in sauvegardes if sauvegarde.type_sauvegarde == 'automatique' %}
                        {% set _ = sauvegardes_auto.append(sauvegarde) %}
                    {% endfor %}
                    
                    {% if sauvegardes_auto %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date de Création</th>
                                        <th>Nom du Fichier</th>
                                        <th>Taille</th>
                                        <th>Description</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sauvegarde in sauvegardes_auto[:5] %}
                                    <tr>
                                        <td>{{ sauvegarde.date_creation.strftime('%d/%m/%Y %H:%M') }}</td>
                                        <td>
                                            <i class="fas fa-robot text-info"></i>
                                            {{ sauvegarde.nom_fichier }}
                                        </td>
                                        <td>{{ sauvegarde.taille_fichier_formatee() }}</td>
                                        <td>{{ sauvegarde.description or '-' }}</td>
                                        <td>
                                            <a href="{{ url_for('telecharger_sauvegarde', id=sauvegarde.id) }}" 
                                               class="btn btn-sm btn-outline-primary" title="Télécharger">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if sauvegardes_auto|length > 5 %}
                        <p class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            Affichage des 5 dernières sauvegardes automatiques. 
                            <a href="{{ url_for('sauvegardes') }}">Voir toutes les sauvegardes</a>
                        </p>
                        {% endif %}
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-robot fa-3x mb-3"></i>
                            <p>Aucune sauvegarde automatique trouvée.</p>
                            <p>Démarrez le planificateur pour commencer les sauvegardes automatiques.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
