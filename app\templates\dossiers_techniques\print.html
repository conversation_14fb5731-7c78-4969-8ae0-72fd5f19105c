{% extends "print_layout.html" %}

{% block content %}
<div class="info-section">
    <h2>Informations Générales</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Entreprise:</span>
                <span>{{ dossier.fiche_inscription.raison_sociale if dossier.fiche_inscription else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Domaine:</span>
                <span>{{ dossier.domaine.nom if dossier.domaine else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Thème:</span>
                <span>{{ dossier.theme.nom if dossier.theme else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Type de Formation:</span>
                <span>{{ dossier.type_formation }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Date de Début:</span>
                <span>{{ dossier.date_debut.strftime('%d/%m/%Y') if dossier.date_debut else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Date de Fin:</span>
                <span>{{ dossier.date_fin.strftime('%d/%m/%Y') if dossier.date_fin else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Lieu:</span>
                <span>{{ dossier.lieu }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Conforme:</span>
                <span>{{ 'Oui' if dossier.conforme else 'Non' }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Financières</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Coût Formation HT:</span>
                <span>{{ dossier.cout_formation_ht }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">TVA (%):</span>
                <span>{{ dossier.tva }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Coût Formation TTC:</span>
                <span>{{ dossier.cout_formation_ttc }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Nombre de Participants:</span>
                <span>{{ dossier.nombre_participants }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Nombre d'Heures:</span>
                <span>{{ dossier.nombre_heures }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Coût Horaire:</span>
                <span>{{ dossier.cout_horaire }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations de l'Organisme</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Organisme:</span>
                <span>{{ dossier.organisme.raison_sociale if dossier.organisme else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Numéro CNSS Organisme:</span>
                <span>{{ dossier.num_cnss_organisme }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Formateur:</span>
                <span>{{ dossier.formateur.nom_prenom if dossier.formateur else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Numéro Facture:</span>
                <span>{{ dossier.num_facture }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Contenu de la Formation</h2>
    <div class="row">
        <div class="col-md-12">
            <div class="info-item">
                <span class="info-label">Objectif:</span>
                <p>{{ dossier.objectif }}</p>
            </div>
            <div class="info-item">
                <span class="info-label">Contenu Indicatif:</span>
                <p>{{ dossier.contenu_indicatif }}</p>
            </div>
            <div class="info-item">
                <span class="info-label">Population Cible:</span>
                <p>{{ dossier.population_cible }}</p>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Complémentaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Date de Création:</span>
                <span>{{ dossier.date_creation.strftime('%d/%m/%Y') if dossier.date_creation else 'N/A' }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Créé par:</span>
                <span>{{ dossier.user.nom_complet if dossier.user else 'N/A' }}</span>
            </div>
        </div>
    </div>
</div>

{% if dossier.dossiers_remboursement %}
<div class="info-section">
    <h2>Dossiers de Remboursement Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Thème</th>
                <th>Date</th>
                <th>Montant</th>
                <th>Mode de Règlement</th>
            </tr>
        </thead>
        <tbody>
            {% for remboursement in dossier.dossiers_remboursement %}
            <tr>
                <td>{{ remboursement.id }}</td>
                <td>{{ remboursement.theme }}</td>
                <td>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else 'N/A' }}</td>
                <td>{{ remboursement.montant }}</td>
                <td>{{ remboursement.mode_de_reglement }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}
{% endblock %}
