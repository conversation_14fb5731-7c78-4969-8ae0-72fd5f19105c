<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ rapport.titre }}</h1>
        <p>Généré le {{ rapport.date_creation.strftime('%d/%m/%Y à %H:%M') }}</p>
    </div>

    <div class="content">
        {{ rapport.contenu|safe }}
    </div>

    <div class="footer">
        <p>Gestion des Formations - Rapport généré par {{ rapport.user.nom_complet }}</p>
    </div>

    <div class="no-print text-center mt-4">
        <button class="btn btn-primary" onclick="window.print()">Imprimer</button>
        <a href="{{ url_for('view_rapport', id=rapport.id) }}" class="btn btn-secondary">Retour</a>
    </div>
</body>
</html>
