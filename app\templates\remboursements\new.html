{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-file-invoice-dollar"></i> Nouveau Dossier de Remboursement</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations du Dossier de Remboursement</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.fiche_inscription.label(class="form-control-label") }}
                            {{ form.fiche_inscription(class="form-control") }}
                            {% if form.fiche_inscription.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.fiche_inscription.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.organisme.label(class="form-control-label") }}
                            {{ form.organisme(class="form-control") }}
                            {% if form.organisme.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.organisme.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.formateur.label(class="form-control-label") }}
                            {{ form.formateur(class="form-control") }}
                            {% if form.formateur.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.formateur.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.theme.label(class="form-control-label") }}
                            {{ form.theme(class="form-control") }}
                            {% if form.theme.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.theme.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date.label(class="form-control-label") }}
                            {{ form.date(class="form-control", type="date") }}
                            {% if form.date.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.facturation.label(class="form-control-label") }}
                            {{ form.facturation(class="form-control") }}
                            {% if form.facturation.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.facturation.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.contrat.label(class="form-control-label") }}
                            {{ form.contrat(class="form-control") }}
                            {% if form.contrat.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.contrat.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.mode_de_reglement.label(class="form-control-label") }}
                            {{ form.mode_de_reglement(class="form-control") }}
                            {% if form.mode_de_reglement.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.mode_de_reglement.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            {{ form.f2(class="form-check-input") }}
                            {{ form.f2.label(class="form-check-label") }}
                            {% if form.f2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.f2.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            {{ form.liste_de_presence(class="form-check-input") }}
                            {{ form.liste_de_presence.label(class="form-check-label") }}
                            {% if form.liste_de_presence.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.liste_de_presence.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            {{ form.f4(class="form-check-input") }}
                            {{ form.f4.label(class="form-check-label") }}
                            {% if form.f4.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.f4.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mb-3">
                            {{ form.m6(class="form-check-input") }}
                            {{ form.m6.label(class="form-check-label") }}
                            {% if form.m6.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.m6.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            {{ form.fiche_synthetique_evaluation_formateur(class="form-check-input") }}
                            {{ form.fiche_synthetique_evaluation_formateur.label(class="form-check-label") }}
                            {% if form.fiche_synthetique_evaluation_formateur.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.fiche_synthetique_evaluation_formateur.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    {{ form.piece_jointe.label(class="form-control-label") }}
                    {{ form.piece_jointe(class="form-control-file") }}
                    {% if form.piece_jointe.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.piece_jointe.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-text text-muted">Formats acceptés: PDF, PNG, JPG, JPEG</small>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('remboursements') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
