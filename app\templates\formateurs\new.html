{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-chalkboard-teacher"></i> Nouveau Formateur</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations du Formateur</h5>
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.nom_prenom.label(class="form-control-label") }}
                            {{ form.nom_prenom(class="form-control") }}
                            {% if form.nom_prenom.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nom_prenom.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.specialite.label(class="form-control-label") }}
                            {{ form.specialite(class="form-control") }}
                            {% if form.specialite.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.specialite.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.adresse.label(class="form-control-label") }}
                            {{ form.adresse(class="form-control") }}
                            {% if form.adresse.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.adresse.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.ville.label(class="form-control-label") }}
                            {{ form.ville(class="form-control") }}
                            {% if form.ville.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.ville.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.num_tel.label(class="form-control-label") }}
                            {{ form.num_tel(class="form-control") }}
                            {% if form.num_tel.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.num_tel.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.cv(class="form-check-input") }}
                            {{ form.cv.label(class="form-check-label") }}
                            {% if form.cv.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.cv.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.diplomes(class="form-check-input") }}
                            {{ form.diplomes.label(class="form-check-label") }}
                            {% if form.diplomes.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.diplomes.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    {{ form.piece_jointe.label(class="form-control-label") }}
                    {{ form.piece_jointe(class="form-control-file") }}
                    {% if form.piece_jointe.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.piece_jointe.errors %}
                                <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                    {% endif %}
                    <small class="form-text text-muted">Formats acceptés: PDF, PNG, JPG, JPEG</small>
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('formateurs') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
