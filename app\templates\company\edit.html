{% extends "base.html" %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/company.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête de la page -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="fas fa-edit text-primary me-2"></i>
                        {% if company.id %}Modifier{% else %}Ajouter{% endif %} les Informations de l'Entreprise
                    </h1>
                    <p class="text-muted mb-0">Configuration des informations pour l'impression et les documents</p>
                </div>
                <div>
                    <a href="{{ url_for('company_info') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>

            <!-- Formulaire -->
            <form method="POST" enctype="multipart/form-data">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <!-- Colonne principale -->
                    <div class="col-lg-8">
                        <!-- Informations générales -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-info-circle me-2"></i>Informations Générales
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.nom_entreprise.label(class="form-label fw-bold") }}
                                            {{ form.nom_entreprise(class="form-control") }}
                                            {% if form.nom_entreprise.errors %}
                                                <div class="text-danger small">
                                                    {% for error in form.nom_entreprise.errors %}
                                                        <div>{{ error }}</div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.raison_sociale.label(class="form-label fw-bold") }}
                                            {{ form.raison_sociale(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.forme_juridique.label(class="form-label fw-bold") }}
                                            {{ form.forme_juridique(class="form-select") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.slogan.label(class="form-label fw-bold") }}
                                            {{ form.slogan(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.description.label(class="form-label fw-bold") }}
                                            {{ form.description(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Informations de contact -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-address-book me-2"></i>Informations de Contact
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.adresse.label(class="form-label fw-bold") }}
                                            {{ form.adresse(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.ville.label(class="form-label fw-bold") }}
                                            {{ form.ville(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.code_postal.label(class="form-label fw-bold") }}
                                            {{ form.code_postal(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.pays.label(class="form-label fw-bold") }}
                                            {{ form.pays(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.telephone_1.label(class="form-label fw-bold") }}
                                            {{ form.telephone_1(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.telephone_2.label(class="form-label fw-bold") }}
                                            {{ form.telephone_2(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.fax.label(class="form-label fw-bold") }}
                                            {{ form.fax(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.email.label(class="form-label fw-bold") }}
                                            {{ form.email(class="form-control") }}
                                            {% if form.email.errors %}
                                                <div class="text-danger small">
                                                    {% for error in form.email.errors %}
                                                        <div>{{ error }}</div>
                                                    {% endfor %}
                                                </div>
                                            {% endif %}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.site_web.label(class="form-label fw-bold") }}
                                            {{ form.site_web(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Informations légales -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-gavel me-2"></i>Informations Légales
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.patente.label(class="form-label fw-bold") }}
                                            {{ form.patente(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.identifiant_fiscal.label(class="form-label fw-bold") }}
                                            {{ form.identifiant_fiscal(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.num_rc.label(class="form-label fw-bold") }}
                                            {{ form.num_rc(class="form-control") }}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            {{ form.num_cnss.label(class="form-label fw-bold") }}
                                            {{ form.num_cnss(class="form-control") }}
                                        </div>
                                        <div class="mb-3">
                                            {{ form.ice.label(class="form-label fw-bold") }}
                                            {{ form.ice(class="form-control") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Informations pour l'impression -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-file-alt me-2"></i>Informations pour l'Impression
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    {{ form.pied_de_page.label(class="form-label fw-bold") }}
                                    {{ form.pied_de_page(class="form-control") }}
                                    <div class="form-text">Texte qui apparaîtra en bas des documents imprimés</div>
                                </div>
                                <div class="mb-3">
                                    {{ form.mentions_legales.label(class="form-label fw-bold") }}
                                    {{ form.mentions_legales(class="form-control") }}
                                    <div class="form-text">Mentions légales pour les documents officiels</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Colonne latérale -->
                    <div class="col-lg-4">
                        <!-- Logo et couleurs -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-palette me-2"></i>Identité Visuelle
                                </h6>
                            </div>
                            <div class="card-body">
                                <!-- Logo actuel -->
                                {% if company and company.logo_path %}
                                <div class="mb-3 text-center">
                                    <label class="form-label fw-bold">Logo actuel</label>
                                    <div>
                                        <img src="{{ url_for('static', filename=company.logo_path) }}" 
                                             alt="Logo actuel" class="img-fluid" style="max-height: 100px;">
                                    </div>
                                </div>
                                {% endif %}
                                
                                <!-- Upload nouveau logo -->
                                <div class="mb-3">
                                    {{ form.logo.label(class="form-label fw-bold") }}
                                    {{ form.logo(class="form-control") }}
                                    {% if form.logo.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.logo.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">Formats acceptés: JPG, JPEG, PNG, GIF</div>
                                </div>
                                
                                <!-- Couleurs -->
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            {{ form.couleur_principale.label(class="form-label fw-bold") }}
                                            {{ form.couleur_principale(class="form-control form-control-color") }}
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            {{ form.couleur_secondaire.label(class="form-label fw-bold") }}
                                            {{ form.couleur_secondaire(class="form-control form-control-color") }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Statut -->
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="fas fa-toggle-on me-2"></i>Statut
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch">
                                    {{ form.actif(class="form-check-input") }}
                                    {{ form.actif.label(class="form-check-label fw-bold") }}
                                    <div class="form-text">Activer ces informations pour l'utilisation dans l'application</div>
                                </div>
                            </div>
                        </div>

                        <!-- Boutons d'action -->
                        <div class="card shadow">
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    {{ form.submit(class="btn btn-primary btn-lg") }}
                                    <a href="{{ url_for('company_info') }}" class="btn btn-secondary">Annuler</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Prévisualisation des couleurs
document.addEventListener('DOMContentLoaded', function() {
    const couleurPrincipale = document.getElementById('couleur_principale');
    const couleurSecondaire = document.getElementById('couleur_secondaire');
    
    if (couleurPrincipale) {
        couleurPrincipale.addEventListener('change', function() {
            console.log('Couleur principale changée:', this.value);
        });
    }
    
    if (couleurSecondaire) {
        couleurSecondaire.addEventListener('change', function() {
            console.log('Couleur secondaire changée:', this.value);
        });
    }
});
</script>
{% endblock %}
