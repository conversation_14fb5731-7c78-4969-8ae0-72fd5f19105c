// Script pour gérer le mode sombre

document.addEventListener('DOMContentLoaded', function() {
    const darkModeToggle = document.getElementById('darkModeToggle');

    if (darkModeToggle) {
        // Vérifier si le mode sombre est déjà activé dans le localStorage
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
            updateDarkModeIcon(true);
        }

        // Ajouter l'événement de clic pour basculer le mode sombre
        darkModeToggle.addEventListener('click', function() {
            const isDarkMode = document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', isDarkMode);
            updateDarkModeIcon(isDarkMode);
        });
    }

    // Mettre à jour l'icône en fonction du mode actuel
    function updateDarkModeIcon(isDarkMode) {
        const icon = darkModeToggle.querySelector('i');
        if (icon) {
            if (isDarkMode) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }
    }
});

// Fonction pour ajouter des animations aux formulaires
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter une classe pour déclencher l'animation lorsque la page est chargée
    const formElements = document.querySelectorAll('.animate-form-element');

    setTimeout(() => {
        formElements.forEach((element, index) => {
            setTimeout(() => {
                element.classList.add('animate-in');
            }, index * 100); // Ajouter un délai progressif pour chaque élément
        });
    }, 300);
});

// Fonction pour afficher un spinner lors de la soumission du formulaire
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const submitButton = document.querySelector('button[type="submit"]');

    if (loginForm && submitButton) {
        loginForm.addEventListener('submit', function() {
            // Désactiver le bouton et afficher le spinner
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Authentification en cours...';
        });
    }
});
