{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-calendar-alt"></i> Modifier Rendez-vous</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations du Rendez-vous</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.formateur.label(class="form-control-label") }}
                            {{ form.formateur(class="form-control") }}
                            {% if form.formateur.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.formateur.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.date_rendezvous.label(class="form-control-label") }}
                            {{ form.date_rendezvous(class="form-control", type="date") }}
                            {% if form.date_rendezvous.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.date_rendezvous.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            {{ form.description.label(class="form-control-label") }}
                            {{ form.description(class="form-control", rows=3) }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.description.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            {{ form.couleur.label(class="form-control-label") }}
                            <div class="input-group">
                                {{ form.couleur(class="form-control", type="color", value=form.couleur.data or "#007bff") }}
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="fas fa-palette"></i>
                                    </span>
                                </div>
                            </div>
                            {% if form.couleur.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.couleur.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Choisissez une couleur pour identifier ce rendez-vous</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('agenda_formateurs') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
