# نظام إدارة التكوين

نظام شامل لإدارة التكوين والتدريب مبني بـ Flask.

## التشغيل السريع

### الطريقة الأولى (الأسهل):
انقر مرتين على الملف: `SOLUTION_FINALE.bat`

### الطريقة الثانية:
1. تثبيت Python 3.11 من python.org
2. تشغيل: `START.bat`

### الطريقة الثالثة:
```bash
python run.py
```

## معلومات الدخول

- **الرابط:** http://127.0.0.1:8080
- **المستخدم:** admin
- **كلمة المرور:** admin123

## الملفات الأساسية

- `run.py` - ملف التشغيل الرئيسي
- `config.py` - إعدادات التطبيق
- `init_db.py` - إنشاء قاعدة البيانات
- `requirements.txt` - المتطلبات
- `app/` - مجلد التطبيق الرئيسي

## المميزات

- إدارة المتدربين والمدربين
- إدارة الدورات التدريبية
- نظام المستخدمين والصلاحيات
- واجهة عربية سهلة الاستخدام
