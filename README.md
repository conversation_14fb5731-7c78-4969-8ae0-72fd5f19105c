# نظام إدارة التكوين

نظام شامل لإدارة التكوين والتدريب مبني بـ Flask.

## التشغيل السريع

### الطريقة الأولى (الأسهل):
انقر مرتين على الملف: `SOLUTION_FINALE.bat`

### الطريقة الثانية (محسنة):
انقر مرتين على الملف: `START_IMPROVED.bat` (مع فحص شامل)

### الطريقة الثالثة:
1. تثبيت Python 3.11 من python.org
2. تشغيل: `START.bat`

### الطريقة الرابعة:
```bash
python run.py
```

## معلومات الدخول

- **الرابط:** http://127.0.0.1:8080
- **المستخدم:** admin
- **كلمة المرور:** admin123

## الملفات الأساسية

- `run.py` - ملف التشغيل الرئيسي (محسن مع فحص شامل)
- `START_IMPROVED.bat` - ملف تشغيل محسن مع فحص المتطلبات
- `RUN_APP.bat` - ملف التشغيل الأصلي
- `config.py` - إعدادات التطبيق
- `init_db.py` - إنشاء قاعدة البيانات
- `requirements.txt` - المتطلبات
- `app/` - مجلد التطبيق الرئيسي

## المميزات

- 📋 إدارة الفيش التسجيل
- 📁 إدارة الملفات التقنية
- 💰 إدارة المردودات
- 🏢 إدارة المؤسسات
- 👨‍🏫 إدارة المكونين
- 📅 أجندة المكونين
- 🎓 إدارة المجالات والمواضيع
- 👥 إدارة المستخدمين والصلاحيات
- 📊 التقارير والإحصائيات
- 💾 النسخ الاحتياطي
- 📝 سجل الأنشطة
- 🌐 واجهة عربية سهلة الاستخدام

## 🔧 التحسينات الجديدة

- ✅ فحص تلقائي للمتطلبات عند التشغيل
- ✅ إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة
- ✅ فتح المتصفح تلقائياً
- ✅ رسائل خطأ واضحة ومفيدة
- ✅ واجهة تشغيل محسنة مع رموز تعبيرية
- ✅ فحص شامل للنظام قبل التشغيل

## 🛠️ استكشاف الأخطاء

إذا واجهت مشاكل:

1. **تأكد من تثبيت Python**: `python --version`
2. **ثبت المتطلبات**: `pip install -r requirements.txt`
3. **استخدم الملف المحسن**: `START_IMPROVED.bat`
4. **تحقق من الرسائل**: البرنامج سيعرض رسائل واضحة عن أي مشاكل
