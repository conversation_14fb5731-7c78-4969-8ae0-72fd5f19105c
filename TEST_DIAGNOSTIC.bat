@echo off
chcp 65001 >nul
title تشخيص نظام إدارة التكوين

cls
echo.
echo ============================================================
echo              🔍 تشخيص نظام إدارة التكوين
echo ============================================================
echo.

echo 📋 فحص الملفات الأساسية...
echo.

REM فحص الملفات المطلوبة
if exist "run.py" (
    echo ✅ run.py موجود
) else (
    echo ❌ run.py غير موجود
)

if exist "config.py" (
    echo ✅ config.py موجود
) else (
    echo ❌ config.py غير موجود
)

if exist "app" (
    echo ✅ مجلد app موجود
) else (
    echo ❌ مجلد app غير موجود
)

if exist "dist\GestionFormation.exe" (
    echo ✅ GestionFormation.exe موجود
) else (
    echo ❌ GestionFormation.exe غير موجود
)

if exist "formations_central.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها
)

echo.
echo ============================================================
echo 🔧 فحص Python...
echo ============================================================

python --version 2>nul
if %errorlevel% equ 0 (
    echo ✅ Python متوفر
    python --version
) else (
    echo ❌ Python غير متوفر أو غير مُعرف
)

echo.
echo ============================================================
echo 🚀 اختبار تشغيل الملف التنفيذي...
echo ============================================================

echo 📝 محاولة تشغيل GestionFormation.exe...
echo ⏱️ انتظار 5 ثوانٍ...

start "" "dist\GestionFormation.exe"
timeout /t 5 /nobreak >nul

echo.
echo 🌐 محاولة فتح المتصفح...
start "" "http://127.0.0.1:8080"

echo.
echo ============================================================
echo 📋 تعليمات:
echo ============================================================
echo 1. تحقق من فتح نافذة سوداء (Console)
echo 2. تحقق من فتح المتصفح على الرابط
echo 3. جرب تسجيل الدخول بـ admin/admin123
echo 4. أخبرني بأي رسائل خطأ تظهر
echo ============================================================

pause
