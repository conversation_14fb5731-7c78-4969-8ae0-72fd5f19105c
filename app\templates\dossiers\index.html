{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-folder"></i> Dossiers Techniques</h1>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('new_dossier_technique') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle"></i> Nouveau Dossier
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_dossiers') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un dossier technique..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Dossiers Techniques</h5>
        </div>
        <div class="card-body">
            {% if dossiers %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Entreprise</th>
                                <th>Domaine</th>
                                <th>Thème</th>
                                <th>Type de Formation</th>
                                <th>Coût HT</th>
                                <th>Validation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dossier in dossiers %}
                                <tr>
                                    <td>{{ dossier.id }}</td>
                                    <td>{{ dossier.fiche_inscription.raison_sociale }}</td>
                                    <td>{{ dossier.domaine.nom }}</td>
                                    <td>{{ dossier.theme.nom }}</td>
                                    <td>{{ dossier.type_formation }}</td>
                                    <td>{{ dossier.cout_formation_ht }} DH</td>
                                    <td>
                                        {% if dossier.validation %}
                                            <span class="badge badge-success">Validé</span>
                                        {% else %}
                                            <span class="badge badge-warning">En attente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{{ url_for('delete_dossier_technique', id=dossier.id) }}" class="btn btn-sm btn-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce dossier?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    Aucun dossier technique trouvé. <a href="{{ url_for('new_dossier_technique') }}">Créer un nouveau dossier</a>.
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='dossiers_techniques') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
