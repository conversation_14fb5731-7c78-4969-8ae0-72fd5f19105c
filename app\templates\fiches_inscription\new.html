{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ title }}</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.date_inscription.label(class="form-control-label") }}
                                    {{ form.date_inscription(class="form-control", type="date") }}
                                    {% for error in form.date_inscription.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.raison_sociale.label(class="form-control-label") }}
                                    {{ form.raison_sociale(class="form-control") }}
                                    {% for error in form.raison_sociale.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.tel_entreprise.label(class="form-control-label") }}
                                    {{ form.tel_entreprise(class="form-control") }}
                                    {% for error in form.tel_entreprise.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.fax_entreprise.label(class="form-control-label") }}
                                    {{ form.fax_entreprise(class="form-control") }}
                                    {% for error in form.fax_entreprise.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.email.label(class="form-control-label") }}
                                    {{ form.email(class="form-control") }}
                                    {% for error in form.email.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.mot_de_passe_email.label(class="form-control-label") }}
                                    {{ form.mot_de_passe_email(class="form-control") }}
                                    {% for error in form.mot_de_passe_email.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.patente.label(class="form-control-label") }}
                                    {{ form.patente(class="form-control") }}
                                    {% for error in form.patente.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.identifiant_fiscale.label(class="form-control-label") }}
                                    {{ form.identifiant_fiscale(class="form-control") }}
                                    {% for error in form.identifiant_fiscale.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.num_rc.label(class="form-control-label") }}
                                    {{ form.num_rc(class="form-control") }}
                                    {% for error in form.num_rc.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.num_cnss.label(class="form-control-label") }}
                                    {{ form.num_cnss(class="form-control") }}
                                    {% for error in form.num_cnss.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.eligible(class="form-check-input") }}
                                        {{ form.eligible.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    {{ form.ice.label(class="form-control-label") }}
                                    {{ form.ice(class="form-control") }}
                                    {% for error in form.ice.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.mot_de_passe_ice.label(class="form-control-label") }}
                                    {{ form.mot_de_passe_ice(class="form-control") }}
                                    {% for error in form.mot_de_passe_ice.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.nombre_cadres.label(class="form-control-label") }}
                                    {{ form.nombre_cadres(class="form-control") }}
                                    {% for error in form.nombre_cadres.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.nombre_employes.label(class="form-control-label") }}
                                    {{ form.nombre_employes(class="form-control") }}
                                    {% for error in form.nombre_employes.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.nombre_ouvriers.label(class="form-control-label") }}
                                    {{ form.nombre_ouvriers(class="form-control") }}
                                    {% for error in form.nombre_ouvriers.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.validation(class="form-check-input") }}
                                        {{ form.validation.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    {{ form.date_validation.label(class="form-control-label") }}
                                    {{ form.date_validation(class="form-control") }}
                                    {% for error in form.date_validation.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    <div class="form-check">
                                        {{ form.depot_physique(class="form-check-input") }}
                                        {{ form.depot_physique.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="form-group">
                                    {{ form.date_depot.label(class="form-control-label") }}
                                    {{ form.date_depot(class="form-control") }}
                                    {% for error in form.date_depot.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.piece_jointe.label(class="form-control-label") }}
                                    {{ form.piece_jointe(class="form-control-file") }}
                                    {% for error in form.piece_jointe.errors %}
                                    <span class="text-danger">{{ error }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('fiches_inscription') }}" class="btn btn-secondary">Annuler</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
