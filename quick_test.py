#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للتطبيق
"""

try:
    print("🔍 اختبار الاستيرادات...")
    from app import create_app
    print("✅ استيراد التطبيق - OK")
    
    print("🔍 اختبار إنشاء التطبيق...")
    app = create_app()
    print("✅ إنشاء التطبيق - OK")
    
    print("🔍 اختبار المسارات...")
    with app.test_client() as client:
        response = client.get('/')
        print(f"✅ الصفحة الرئيسية - كود: {response.status_code}")
        
        response = client.get('/login')
        print(f"✅ صفحة تسجيل الدخول - كود: {response.status_code}")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("✅ البرنامج يعمل بشكل صحيح")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
