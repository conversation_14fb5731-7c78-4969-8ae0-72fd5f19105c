/* Style personnalisé pour l'application */

/* Variables CSS pour le mode sombre */
:root {
    --primary-color: #4e73df;
    --secondary-color: #6c757d;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --light-color: #f8f9fc;
    --dark-color: #343a40;

    --bg-primary: #fff;
    --bg-secondary: #f8f9fc;
    --text-primary: #333;
    --text-secondary: #6c757d;
    --border-color: #e3e6f0;
    --card-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

/* Mode sombre */
body.dark-mode {
    --bg-primary: #1e2124;
    --bg-secondary: #2c2f33;
    --text-primary: #f8f9fc;
    --text-secondary: #b3b8bd;
    --border-color: #4e5d6c;
    --card-shadow: 0 0.15rem 1.75rem 0 rgba(0, 0, 0, 0.5);
}

/* Styles généraux */
body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Style pour la page de connexion */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('../images/grungy-white-background-natural-cement-stone-old-texture-as-retro-pattern-wall-conceptual-wall-banner-grunge-material-construction.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 2rem 0;
}

.login-card {
    background-color: var(--bg-primary);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    width: 100%;
    max-width: 900px;
    display: flex;
    flex-direction: row;
}

.login-form-container {
    padding: 2.5rem;
    width: 50%;
}

.login-image-container {
    width: 50%;
    background-color: var(--primary-color);
    background-image: url('../images/5218235.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.login-image-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(78, 115, 223, 0.7);
    z-index: 1;
}

.login-image-container img {
    position: relative;
    z-index: 2;
    max-width: 80%;
    height: auto;
    margin-bottom: 1rem;
}

.login-description {
    position: relative;
    z-index: 2;
    color: white;
    text-align: center;
    padding: 0 1.5rem;
}

.login-description h3 {
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.login-description p {
    font-size: 0.9rem;
    line-height: 1.5;
}

.login-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.login-subtitle {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.form-control {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: #3a5fc9;
    border-color: #3a5fc9;
    transform: translateY(-2px);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Animation pour les champs de formulaire */
.form-floating label {
    padding: 0.75rem 1rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    transform: scale(0.85) translateY(-0.75rem) translateX(0.15rem);
}

/* Bouton de basculement du mode sombre */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
}

/* Responsive */
@media (max-width: 768px) {
    .login-card {
        flex-direction: column;
    }

    .login-form-container,
    .login-image-container {
        width: 100%;
    }

    .login-image-container {
        order: -1;
        padding: 1.5rem;
    }
}
