{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-file-invoice-dollar"></i> Dossiers de Remboursement</h1>
        </div>
        <div class="col-md-4 text-right">
            <a href="{{ url_for('new_remboursement') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle"></i> Nouveau Dossier
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <form action="{{ url_for('search_remboursements') }}" method="GET">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Rechercher un dossier..." value="{{ query if query else '' }}">
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> Rechercher
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Liste des Dossiers de Remboursement</h5>
        </div>
        <div class="card-body">
            {% if remboursements %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Entreprise</th>
                                <th>Organisme</th>
                                <th>Formateur</th>
                                <th>Thème</th>
                                <th>Date</th>
                                <th>Facturation</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for remboursement in remboursements %}
                                <tr>
                                    <td>{{ remboursement.id }}</td>
                                    <td>{{ remboursement.fiche_inscription.raison_sociale if remboursement.fiche_inscription else '-' }}</td>
                                    <td>{{ remboursement.organisme.raison_sociale if remboursement.organisme else '-' }}</td>
                                    <td>{{ remboursement.formateur.nom_prenom if remboursement.formateur else '-' }}</td>
                                    <td>{{ remboursement.theme }}</td>
                                    <td>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else '-' }}</td>
                                    <td>{{ remboursement.facturation }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('edit_remboursement', id=remboursement.id) }}" class="btn btn-sm btn-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ url_for('print_remboursement', id=remboursement.id) }}" class="btn btn-sm btn-info" title="Imprimer" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            <a href="{{ url_for('delete_remboursement', id=remboursement.id) }}" class="btn btn-sm btn-danger" title="Supprimer" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce dossier?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    Aucun dossier de remboursement trouvé. <a href="{{ url_for('new_remboursement') }}">Créer un nouveau dossier</a>.
                </div>
            {% endif %}
        </div>
    </div>

    <div class="mt-4">
        <a href="{{ url_for('generate_report', type='remboursements') }}" class="btn btn-success">
            <i class="fas fa-print"></i> Imprimer le rapport
        </a>
    </div>
</div>
{% endblock %}
