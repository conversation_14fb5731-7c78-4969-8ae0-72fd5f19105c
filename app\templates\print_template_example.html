<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemple de Document avec Informations Entreprise</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/company.css') }}">
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none !important; }
            .print-header { 
                margin-bottom: 30px; 
                border-bottom: 2px solid #000;
                padding-bottom: 15px;
            }
            .print-footer {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                height: 60px;
                border-top: 1px solid #000;
                padding-top: 10px;
                background: white;
                font-size: 10px;
                text-align: center;
            }
            .print-content {
                margin-bottom: 80px;
            }
        }
        
        .company-header-print {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .company-logo-container {
            flex: 0 0 auto;
        }
        
        .company-info-container {
            flex: 1;
            text-align: center;
        }
        
        .company-name-print {
            font-size: 24px;
            font-weight: bold;
            color: {{ get_company_colors().primary }};
            margin: 0;
        }
        
        .company-slogan-print {
            font-style: italic;
            color: #666;
            margin: 5px 0;
        }
        
        .company-contact-print {
            font-size: 12px;
            color: #333;
        }
    </style>
</head>
<body>
    <!-- Bouton d'impression (masqué à l'impression) -->
    <div class="container-fluid no-print">
        <div class="row">
            <div class="col-12 text-center py-3">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>Imprimer ce document
                </button>
                <a href="{{ url_for('dashboard') }}" class="btn btn-secondary ms-2">
                    <i class="fas fa-arrow-left me-2"></i>Retour au tableau de bord
                </a>
            </div>
        </div>
    </div>

    <!-- En-tête avec informations de l'entreprise -->
    <div class="print-header">
        {% if company_info %}
        <div class="company-header-print">
            <!-- Logo -->
            {% if get_company_logo_url() %}
            <div class="company-logo-container">
                <img src="{{ get_company_logo_url() }}" alt="Logo" class="company-logo-print" style="max-height: 80px;">
            </div>
            {% endif %}
            
            <!-- Informations de l'entreprise -->
            <div class="company-info-container">
                <h1 class="company-name-print">{{ company_info.nom_entreprise }}</h1>
                {% if company_info.slogan %}
                <p class="company-slogan-print">{{ company_info.slogan }}</p>
                {% endif %}
                
                <div class="company-contact-print">
                    {% if company_info.get_full_address() %}
                    <div>{{ company_info.get_full_address() }}</div>
                    {% endif %}
                    
                    <div>
                        {% if company_info.telephone_1 %}Tél: {{ company_info.telephone_1 }}{% endif %}
                        {% if company_info.email %} | Email: {{ company_info.email }}{% endif %}
                        {% if company_info.site_web %} | Web: {{ company_info.site_web }}{% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="text-center">
            <h1>Document de Formation</h1>
            <p class="text-muted">Aucune information d'entreprise configurée</p>
        </div>
        {% endif %}
    </div>

    <!-- Contenu principal du document -->
    <div class="container print-content">
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">Exemple de Document avec En-tête Entreprise</h2>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Informations du Document</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Date:</strong> {{ moment().strftime('%d/%m/%Y') if moment else '03/07/2025' }}</p>
                                <p><strong>Heure:</strong> {{ moment().strftime('%H:%M') if moment else '12:30' }}</p>
                                <p><strong>Type:</strong> Document d'exemple</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Référence:</strong> DOC-{{ moment().strftime('%Y%m%d') if moment else '20250703' }}-001</p>
                                <p><strong>Statut:</strong> <span class="badge bg-success">Actif</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h3>Contenu du Document</h3>
                    <p>Ceci est un exemple de document qui utilise les informations de l'entreprise configurées dans le système.</p>
                    
                    <p>Les éléments suivants sont automatiquement intégrés:</p>
                    <ul>
                        <li><strong>Logo de l'entreprise</strong> - Affiché en haut du document</li>
                        <li><strong>Nom et slogan</strong> - Titre principal du document</li>
                        <li><strong>Informations de contact</strong> - Adresse, téléphone, email, site web</li>
                        <li><strong>Couleurs de l'entreprise</strong> - Appliquées au design</li>
                        <li><strong>Pied de page personnalisé</strong> - Affiché en bas de chaque page</li>
                        <li><strong>Mentions légales</strong> - Informations légales de l'entreprise</li>
                    </ul>

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Comment utiliser ce modèle</h6>
                        <p class="mb-0">
                            Ce modèle peut être adapté pour tous vos documents d'impression:
                            fiches d'inscription, dossiers techniques, certificats, etc.
                            Les informations de l'entreprise sont automatiquement récupérées
                            depuis la configuration dans "Informations Entreprise".
                        </p>
                    </div>
                </div>

                <!-- Exemple de tableau -->
                <div class="mt-4">
                    <h4>Exemple de Données Tabulaires</h4>
                    <table class="table table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>Formation</th>
                                <th>Durée</th>
                                <th>Formateur</th>
                                <th>Statut</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Développement Web</td>
                                <td>40 heures</td>
                                <td>Ahmed BENALI</td>
                                <td><span class="badge bg-success">Terminée</span></td>
                            </tr>
                            <tr>
                                <td>Gestion de Projet</td>
                                <td>30 heures</td>
                                <td>Fatima ALAOUI</td>
                                <td><span class="badge bg-warning">En cours</span></td>
                            </tr>
                            <tr>
                                <td>Marketing Digital</td>
                                <td>25 heures</td>
                                <td>Omar TAZI</td>
                                <td><span class="badge bg-primary">Planifiée</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pied de page avec informations de l'entreprise -->
    <div class="print-footer">
        {% if company_info %}
            {% if company_info.pied_de_page %}
            <div style="margin-bottom: 5px;">
                {{ company_info.pied_de_page.replace('\n', ' | ') }}
            </div>
            {% endif %}
            
            {% if company_info.mentions_legales %}
            <div style="font-size: 8px; color: #666;">
                {{ company_info.mentions_legales.replace('\n', ' | ') }}
            </div>
            {% endif %}
        {% else %}
        <div>
            Document généré par le système de gestion des formations
        </div>
        {% endif %}
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fonction pour l'impression
        function printDocument() {
            window.print();
        }

        // Raccourci clavier pour l'impression
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printDocument();
            }
        });

        // Mettre à jour la date et l'heure en temps réel
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('fr-FR');
            const timeStr = now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});
            const refStr = 'DOC-' + now.getFullYear() +
                          String(now.getMonth() + 1).padStart(2, '0') +
                          String(now.getDate()).padStart(2, '0') + '-001';

            // Mettre à jour les éléments si ils existent
            const dateElements = document.querySelectorAll('p strong');
            dateElements.forEach(el => {
                if (el.textContent === 'Date:') {
                    el.parentNode.innerHTML = `<strong>Date:</strong> ${dateStr}`;
                } else if (el.textContent === 'Heure:') {
                    el.parentNode.innerHTML = `<strong>Heure:</strong> ${timeStr}`;
                } else if (el.textContent === 'Référence:') {
                    el.parentNode.innerHTML = `<strong>Référence:</strong> ${refStr}`;
                }
            });
        });
    </script>
</body>
</html>
