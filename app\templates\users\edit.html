{% extends "base.html" %}

{% block content %}
<style>
.text-purple {
    color: #6f42c1 !important;
}
.form-check:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s;
}
.form-check.p-3.border {
    transition: all 0.2s;
}
.form-check.p-3.border:hover {
    border-color: #007bff !important;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1><i class="fas fa-user-edit"></i> Modifier Utilisateur</h1>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">{{ message }}</div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="card">
        <div class="card-header bg-light">
            <h5 class="mb-0">Informations de l'Utilisateur</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                {{ form.hidden_tag() }}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.username.label(class="form-control-label") }}
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.email.label(class="form-control-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.nom_complet.label(class="form-control-label") }}
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.nom_complet.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            {{ form.password.label(class="form-control-label") }}
                            {{ form.password(class="form-control", placeholder="Laissez vide pour conserver le mot de passe actuel") }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel</small>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            {{ form.is_admin(class="form-check-input") }}
                            {{ form.is_admin.label(class="form-check-label") }}
                            {% if form.is_admin.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_admin.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-key me-2"></i>Permissions et Accès</h5>
                        <small>Modifiez les modules auxquels cet utilisateur a accès</small>
                    </div>
                    <div class="card-body">
                        <!-- Permissions de gestion des données -->
                        <div class="mb-4">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-database me-2"></i>Gestion des Données
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_fiche_inscription(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_fiche_inscription.id }}">
                                            <i class="fas fa-file-alt text-primary me-2"></i>{{ form.perm_fiche_inscription.label.text }}
                                        </label>
                                        <div class="text-muted small">Gestion des fiches d'inscription des formations</div>
                                        {% if form.perm_fiche_inscription.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_fiche_inscription.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_dossier_technique(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_dossier_technique.id }}">
                                            <i class="fas fa-folder-open text-info me-2"></i>{{ form.perm_dossier_technique.label.text }}
                                        </label>
                                        <div class="text-muted small">Accès aux dossiers techniques des formations</div>
                                        {% if form.perm_dossier_technique.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_dossier_technique.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_dossier_remboursement(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_dossier_remboursement.id }}">
                                            <i class="fas fa-money-bill-wave text-success me-2"></i>{{ form.perm_dossier_remboursement.label.text }}
                                        </label>
                                        <div class="text-muted small">Gestion des remboursements de formations</div>
                                        {% if form.perm_dossier_remboursement.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_dossier_remboursement.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_organisme(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_organisme.id }}">
                                            <i class="fas fa-building text-warning me-2"></i>{{ form.perm_organisme.label.text }}
                                        </label>
                                        <div class="text-muted small">Gestion des organismes de formation</div>
                                        {% if form.perm_organisme.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_organisme.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permissions de gestion des ressources -->
                        <div class="mb-4">
                            <h6 class="text-success border-bottom pb-2 mb-3">
                                <i class="fas fa-users me-2"></i>Gestion des Ressources
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_formateur(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_formateur.id }}">
                                            <i class="fas fa-chalkboard-teacher text-purple me-2"></i>{{ form.perm_formateur.label.text }}
                                        </label>
                                        <div class="text-muted small">Gestion des formateurs et leurs informations</div>
                                        {% if form.perm_formateur.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_formateur.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_agenda(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_agenda.id }}">
                                            <i class="fas fa-calendar-alt text-danger me-2"></i>{{ form.perm_agenda.label.text }}
                                        </label>
                                        <div class="text-muted small">Planification et gestion des rendez-vous</div>
                                        {% if form.perm_agenda.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_agenda.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_domaine_theme(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_domaine_theme.id }}">
                                            <i class="fas fa-tags text-secondary me-2"></i>{{ form.perm_domaine_theme.label.text }}
                                        </label>
                                        <div class="text-muted small">Configuration des domaines et thèmes de formation</div>
                                        {% if form.perm_domaine_theme.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_domaine_theme.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_gestion_utilisateurs(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_gestion_utilisateurs.id }}">
                                            <i class="fas fa-users-cog text-dark me-2"></i>{{ form.perm_gestion_utilisateurs.label.text }}
                                        </label>
                                        <div class="text-muted small">Gestion des comptes utilisateurs et permissions</div>
                                        {% if form.perm_gestion_utilisateurs.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_gestion_utilisateurs.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Permissions système -->
                        <div class="mb-4">
                            <h6 class="text-warning border-bottom pb-2 mb-3">
                                <i class="fas fa-cogs me-2"></i>Outils Système
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_rapports(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_rapports.id }}">
                                            <i class="fas fa-chart-bar text-info me-2"></i>{{ form.perm_rapports.label.text }}
                                        </label>
                                        <div class="text-muted small">Génération et consultation des rapports</div>
                                        {% if form.perm_rapports.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_rapports.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_sauvegardes(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_sauvegardes.id }}">
                                            <i class="fas fa-database text-success me-2"></i>{{ form.perm_sauvegardes.label.text }}
                                        </label>
                                        <div class="text-muted small">Gestion des sauvegardes de la base de données</div>
                                        {% if form.perm_sauvegardes.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_sauvegardes.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3 p-3 border rounded">
                                        {{ form.perm_journal_activite(class="form-check-input") }}
                                        <label class="form-check-label fw-bold" for="{{ form.perm_journal_activite.id }}">
                                            <i class="fas fa-history text-warning me-2"></i>{{ form.perm_journal_activite.label.text }}
                                        </label>
                                        <div class="text-muted small">Consultation du journal des activités système</div>
                                        {% if form.perm_journal_activite.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in form.perm_journal_activite.errors %}
                                                    <span>{{ error }}</span>
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions rapides -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-muted mb-3"><i class="fas fa-magic me-2"></i>Actions Rapides</h6>
                            <div class="d-flex gap-2 flex-wrap">
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllPermissions()">
                                    <i class="fas fa-check-double me-1"></i>Tout sélectionner
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deselectAllPermissions()">
                                    <i class="fas fa-times me-1"></i>Tout désélectionner
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectDataPermissions()">
                                    <i class="fas fa-database me-1"></i>Données uniquement
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" onclick="selectBasicPermissions()">
                                    <i class="fas fa-user me-1"></i>Permissions de base
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                function selectAllPermissions() {
                    document.querySelectorAll('input[type="checkbox"][id^="perm_"]').forEach(cb => cb.checked = true);
                }

                function deselectAllPermissions() {
                    document.querySelectorAll('input[type="checkbox"][id^="perm_"]').forEach(cb => cb.checked = false);
                }

                function selectDataPermissions() {
                    deselectAllPermissions();
                    ['perm_fiche_inscription', 'perm_dossier_technique', 'perm_dossier_remboursement', 'perm_organisme'].forEach(id => {
                        const cb = document.getElementById(id);
                        if (cb) cb.checked = true;
                    });
                }

                function selectBasicPermissions() {
                    deselectAllPermissions();
                    ['perm_fiche_inscription', 'perm_organisme', 'perm_formateur'].forEach(id => {
                        const cb = document.getElementById(id);
                        if (cb) cb.checked = true;
                    });
                }
                </script>
                
                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Enregistrer
                    </button>
                    <a href="{{ url_for('users') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
