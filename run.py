#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import webbrowser
import threading
import time

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(3)
    try:
        webbrowser.open('http://127.0.0.1:8080')
    except:
        pass

if __name__ == '__main__':
    try:
        from app import create_app

        app = create_app()

        print("=" * 50)
        print("نظام إدارة التكوين")
        print("=" * 50)
        print("بدء تشغيل الخادم...")
        print("الرابط: http://127.0.0.1:8080")
        print("المستخدم: admin")
        print("كلمة المرور: admin123")
        print("=" * 50)

        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل التطبيق
        app.run(host='0.0.0.0', port=8080, debug=False)

    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")