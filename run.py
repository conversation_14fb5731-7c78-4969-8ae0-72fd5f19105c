#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import webbrowser
import threading
import time
import sys
import os

def check_dependencies():
    """التحقق من وجود المتطلبات الأساسية"""
    required_modules = ['flask', 'flask_sqlalchemy', 'flask_login', 'werkzeug']
    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print("❌ المتطلبات التالية غير مثبتة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\nلتثبيت المتطلبات، قم بتشغيل:")
        print("pip install -r requirements.txt")
        return False

    return True

def check_database():
    """التحقق من وجود قاعدة البيانات"""
    db_path = 'formations_central.db'
    if not os.path.exists(db_path):
        print("⚠️ قاعدة البيانات غير موجودة. سيتم إنشاؤها...")
        try:
            from init_db import init_database
            init_database()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    return True

def open_browser():
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(3)
    try:
        webbrowser.open('http://127.0.0.1:8080')
        print("🌐 تم فتح المتصفح")
    except Exception as e:
        print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")
        print("يمكنك فتح الرابط يدوياً: http://127.0.0.1:8080")

if __name__ == '__main__':
    try:
        print("🔍 فحص المتطلبات...")
        if not check_dependencies():
            input("اضغط Enter للخروج...")
            sys.exit(1)

        print("🔍 فحص قاعدة البيانات...")
        if not check_database():
            input("اضغط Enter للخروج...")
            sys.exit(1)

        print("📦 تحميل التطبيق...")
        from app import create_app
        app = create_app()

        print("=" * 60)
        print("🎯 نظام إدارة التكوين")
        print("=" * 60)
        print("🚀 بدء تشغيل الخادم...")
        print("🌐 الرابط: http://127.0.0.1:8080")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 60)
        print("💡 للإيقاف: اضغط Ctrl+C")
        print("=" * 60)

        # فتح المتصفح في خيط منفصل
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل التطبيق
        app.run(host='0.0.0.0', port=8080, debug=True, threaded=True)

    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم بواسطة المستخدم")
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print(f"نوع الخطأ: {type(e).__name__}")
        import traceback
        print("تفاصيل الخطأ:")
        traceback.print_exc()
        input("اضغط Enter للخروج...")