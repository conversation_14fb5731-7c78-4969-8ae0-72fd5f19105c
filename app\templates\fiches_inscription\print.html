{% extends "print_layout.html" %}

{% block content %}
<div class="info-section">
    <h2>Informations de l'Entreprise</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Raison Sociale:</span>
                <span>{{ fiche.raison_sociale }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Date d'Inscription:</span>
                <span>{{ fiche.date_inscription.strftime('%d/%m/%Y') if fiche.date_inscription else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">ICE:</span>
                <span>{{ fiche.ice }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Numéro CNSS:</span>
                <span>{{ fiche.num_cnss }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Forme Juridique:</span>
                <span>{{ fiche.forme_juridique }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Activité Principale:</span>
                <span>{{ fiche.activite_principale }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Adresse:</span>
                <span>{{ fiche.adresse }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Ville:</span>
                <span>{{ fiche.ville }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Téléphone:</span>
                <span>{{ fiche.tel_entreprise }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span>{{ fiche.email }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Site Web:</span>
                <span>{{ fiche.site_web }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations du Responsable</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Nom et Prénom:</span>
                <span>{{ fiche.nom_prenom_responsable }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Fonction:</span>
                <span>{{ fiche.fonction_responsable }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Téléphone:</span>
                <span>{{ fiche.tel_responsable }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span>{{ fiche.email_responsable }}</span>
            </div>
        </div>
    </div>
</div>

<div class="info-section">
    <h2>Informations Complémentaires</h2>
    <div class="row">
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Date de Création:</span>
                <span>{{ fiche.date_creation.strftime('%d/%m/%Y') if fiche.date_creation else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Éligible:</span>
                <span>{{ 'Oui' if fiche.eligible else 'Non' }}</span>
            </div>
        </div>
        <div class="col-md-6">
            <div class="info-item">
                <span class="info-label">Créé par:</span>
                <span>{{ fiche.user.nom_complet if fiche.user else 'N/A' }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Notes:</span>
                <span>{{ fiche.notes }}</span>
            </div>
        </div>
    </div>
</div>

{% if fiche.dossiers_techniques %}
<div class="info-section">
    <h2>Dossiers Techniques Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Thème</th>
                <th>Coût HT</th>
                <th>Date</th>
                <th>Conforme</th>
            </tr>
        </thead>
        <tbody>
            {% for dossier in fiche.dossiers_techniques %}
            <tr>
                <td>{{ dossier.id }}</td>
                <td>{{ dossier.theme.nom if dossier.theme else 'N/A' }}</td>
                <td>{{ dossier.cout_formation_ht }}</td>
                <td>{{ dossier.date_debut.strftime('%d/%m/%Y') if dossier.date_debut else 'N/A' }}</td>
                <td>{{ 'Oui' if dossier.conforme else 'Non' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

{% if fiche.dossiers_remboursement %}
<div class="info-section">
    <h2>Dossiers de Remboursement Associés</h2>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ID</th>
                <th>Thème</th>
                <th>Organisme</th>
                <th>Date</th>
                <th>Montant</th>
            </tr>
        </thead>
        <tbody>
            {% for remboursement in fiche.dossiers_remboursement %}
            <tr>
                <td>{{ remboursement.id }}</td>
                <td>{{ remboursement.theme }}</td>
                <td>{{ remboursement.organisme.raison_sociale if remboursement.organisme else 'N/A' }}</td>
                <td>{{ remboursement.date.strftime('%d/%m/%Y') if remboursement.date else 'N/A' }}</td>
                <td>{{ remboursement.montant }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}
{% endblock %}
