#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
إنشاء قاعدة بيانات بسيطة
"""

import sqlite3
import os

def create_simple_database():
    """إنشاء قاعدة بيانات بسيطة مع جدول المستخدمين"""
    
    # حذف قاعدة البيانات القديمة إذا كانت موجودة
    if os.path.exists('formations_central.db'):
        os.remove('formations_central.db')
        print("🗑️ تم حذف قاعدة البيانات القديمة")
    
    # إنشاء قاعدة البيانات الجديدة
    conn = sqlite3.connect('formations_central.db')
    cursor = conn.cursor()
    
    # إنشاء جدول المستخدمين
    cursor.execute('''
        CREATE TABLE user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(64) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(128) NOT NULL,
            is_admin BOOLEAN DEFAULT 0,
            nom_complet VARCHAR(100),
            date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
            derniere_connexion DATETIME,
            perm_fiche_inscription BOOLEAN DEFAULT 0,
            perm_dossier_technique BOOLEAN DEFAULT 0,
            perm_dossier_remboursement BOOLEAN DEFAULT 0,
            perm_organisme BOOLEAN DEFAULT 0,
            perm_formateur BOOLEAN DEFAULT 0,
            perm_agenda BOOLEAN DEFAULT 0,
            perm_domaine_theme BOOLEAN DEFAULT 0,
            perm_rapports BOOLEAN DEFAULT 0,
            perm_sauvegardes BOOLEAN DEFAULT 0,
            perm_journal_activite BOOLEAN DEFAULT 0,
            perm_gestion_utilisateurs BOOLEAN DEFAULT 0
        )
    ''')
    
    # إدراج المستخدم الافتراضي
    from werkzeug.security import generate_password_hash
    password_hash = generate_password_hash('admin123')
    
    cursor.execute('''
        INSERT INTO user (
            username, email, password_hash, is_admin, nom_complet,
            perm_fiche_inscription, perm_dossier_technique, perm_dossier_remboursement,
            perm_organisme, perm_formateur, perm_agenda, perm_domaine_theme,
            perm_rapports, perm_sauvegardes, perm_journal_activite, perm_gestion_utilisateurs
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        'admin', '<EMAIL>', password_hash, 1, 'المدير العام',
        1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1
    ))
    
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")

if __name__ == '__main__':
    create_simple_database()
