/* إصلاح سريع للتصميم المبعثر */

/* إعادة تعيين أساسية */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* إصلاح الشريط الجانبي */
.sidebar {
    background-color: #343a40 !important;
    min-height: 100vh;
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar .nav-link {
    color: #adb5bd !important;
    padding: 0.75rem 1rem;
    border-radius: 0;
    margin: 0;
}

.sidebar .nav-link:hover {
    color: white !important;
    background-color: #495057 !important;
}

.sidebar .nav-link.active {
    color: white !important;
    background-color: #007bff !important;
}

/* إصلاح المحتوى الرئيسي */
.main-content {
    margin-left: 250px;
    padding: 2rem;
    min-height: 100vh;
}

/* إصلاح البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.stats-card {
    border: none !important;
    border-radius: 10px !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* إصلاح الألوان */
.bg-primary {
    background-color: #007bff !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

/* إصلاح النافذة العلوية */
.navbar {
    background-color: white !important;
    border-bottom: 1px solid #dee2e6;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-left: 250px;
    position: fixed;
    top: 0;
    right: 0;
    left: 250px;
    z-index: 999;
}

/* إصلاح المحتوى مع النافذة العلوية */
.content-with-navbar {
    margin-top: 80px;
}

/* إصلاح الجداول */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* إصلاح الأزرار */
.btn {
    border-radius: 5px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* إصلاح النماذج */
.form-control {
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* إصلاح التنبيهات */
.alert {
    border-radius: 5px;
    border: none;
    margin-bottom: 1rem;
}

/* إصلاح الشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .navbar {
        left: 0;
        margin-left: 0;
    }
}

/* إصلاح النصوص */
h1, h2, h3, h4, h5, h6 {
    color: #495057;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
}

/* إصلاح الروابط */
a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* إصلاح الأيقونات */
.fas, .far, .fab {
    margin-right: 0.5rem;
}

/* إصلاح التباعد */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.mt-4 {
    margin-top: 1.5rem !important;
}

.p-4 {
    padding: 1.5rem !important;
}

/* إصلاح الحاويات */
.container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
}

.row {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
}

.col, .col-md-6, .col-xl-3 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}
